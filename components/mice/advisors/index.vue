<script setup lang="ts">
// 需求预览-需求对比
import { collapse, Button, message } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import VisibleTable from './components/VisibleTable/index.vue';
import { miceBidManOrderList<PERSON>pi, schemeApi } from '@haierbusiness-front/apis';
import demand_stay from '@/assets/image/advisors/demand_stay.png';
import demand_place from '@/assets/image/advisors/demand_place.png';
import demand_catering from '@/assets/image/advisors/demand_catering.png';
import demand_vehicle from '@/assets/image/advisors/demand_vehicle.png';
import demand_attendant from '@/assets/image/advisors/demand_attendant.png';
import demand_activity from '@/assets/image/advisors/demand_activity.png';
import demand_insurance from '@/assets/image/advisors/demand_insurance.png';
import demand_material from '@/assets/image/advisors/demand_material.png';
import demand_traffic from '@/assets/image/advisors/demand_traffic.png';
import demand_present from '@/assets/image/advisors/demand_present.png';
import demand_other from '@/assets/image/advisors/demand_other.png';

import type { VxeGridProps } from 'vxe-table';
import { ref, reactive, onMounted, defineProps, computed, defineExpose, defineEmits, useAttrs } from 'vue';
import {
  MiceTypeConstantO,
  SelCollapseS,
  MiceDetail,
  hotelLevelAllConstant,
  PlaceUsageTimeTypeConstant,
  MaterialTypeConstant,
  UsagePurposeTypeConstant,
  TableTypeConstant,
  RoomTypeConstant,
  BreakfastTypeConstant,
  HotelDinnerTypeConstant,
  CateringTypeConstant,
  CateringTimeTypeConstant,
  HaveDrinksTypeConstant,
  CarUsageTypeConstant,
  CarBrandTypeConstant,
  SeatTypeConstant,
  UsageTimeTypeConstant,
  AttendantTypeConstant,
  MiceTypeConstant,
} from '@haierbusiness-front/common-libs';
import { getDataBy, resolveParam } from '@haierbusiness-front/utils';

import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
const { loginUser } = storeToRefs(applicationStore());

const orderDetail = reactive({});

const route = useRoute();

const props = defineProps({
  // 预览来源页面
  previewSource: {
    // demandOne - 展示一个、详情接口
    // demandFromUser - 展示一个、查缓存
    // demandContrast - 需求对比
    type: String,
    default: '',
  },
  // 是否管理页面
  isManagePage: {
    type: Boolean,
    default: true,
  },
  // 是否隐藏footer
  hideFooter: {
    type: Boolean,
    default: true,
  },
  // 服务商的类型
  merchantType: {
    // 1-酒店,2-旅行社,3-保险,4-礼品,5-用车
    type: Number,
    default: 0,
  },
  // 平台类型
  platformType: {
    // manage - 平台端
    // user - 用户端
    type: String,
    default: 'manage', // 默认平台端
  },
});

const emit = defineEmits(['hotelListEmit']);

interface ConfigList {
  title?: string;
  cardWidth?: string;
  border?: string;
  gridOptions?: VxeGridProps;
}
const configObj = reactive({
  arr: [],
});

const oldRes = ref<string>('');
const newRes = ref<string>('');

const hideBtn = ref<string>('');
const previewCalc = ref<string>(''); // 是否预览价格

const orderSource = ref<string>('');

const demandRejectReason = ref<string>(''); // 驳回原因

const configListC = async () => {
  // 0,对比  1,原始需求  2,互动需求
  // selCollapseS.selCollapse0 = '0';
  let arr = deepClone(configList);
  if (selCollapseS.selCollapse0 === '1') {
    arr[0].gridOptions.data = [...arr[0].gridOptions.data.slice(0, 1)];
    arr[0].gridOptions.columns.splice(0, 1);
    arr[1].gridOptions.columns.splice(3, 2);
    for (let i = 0; i < arrDate.value.length; i++) {
      arr[2].gridOptions.columns.splice(2 + i, 1);
    }
    arr[3].gridOptions.columns.splice(2, 1);
    arr[4].gridOptions.columns.splice(2, 1);
    arr[5].gridOptions.columns.splice(2, 1);
    arr[1].gridOptions['show-header'] = false;
    arr[2].gridOptions['show-header'] = false;
    arr[3].gridOptions['show-header'] = false;
    arr[4].gridOptions['show-header'] = false;
    arr[5].gridOptions['show-header'] = false;
  } else if (selCollapseS.selCollapse0 === '2') {
    arr[0].gridOptions.data = [...arr[0].gridOptions.data.slice(1, 2)];
    arr[0].gridOptions.columns.splice(0, 1);
    arr[1].gridOptions.columns.splice(1, 2);
    for (let i = 0; i < arrDate.value.length; i++) {
      arr[2].gridOptions.columns.splice(1 + i, 1);
    }
    arr[3].gridOptions.columns.splice(1, 1);
    arr[4].gridOptions.columns.splice(1, 1);
    arr[5].gridOptions.columns.splice(1, 1);
    arr[1].gridOptions['show-header'] = false;
    arr[2].gridOptions['show-header'] = false;
    arr[3].gridOptions['show-header'] = false;
    arr[4].gridOptions['show-header'] = false;
    arr[5].gridOptions['show-header'] = false;
  }
  configObj.arr = arr;
};

// 复制文本到剪贴板
const getCopy = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    message.success('复制成功！');
  } catch (err) {
    message.success('复制失败');
  }
};

const miceDetail = reactive<MiceDetail>({
  miceName: '',
  mainCode: '',
  personTotal: '',
  miceType: '',
  startDate: '',
  endDate: '',
});

const configList = reactive<ConfigList[]>([
  {
    title: '基本需求',
    show: false,
    cardWidth: '80%',
    gridOptions: {
      border: 'inner',
      cellClassName({ row, column, rowIndex, columnIndex }) {
        if (columnIndex > 0) {
          let str1 = configList[0].gridOptions.data[0][column.field];
          let str2 = row[column.field];
          if (str1 != str2 && selCollapseS.selCollapse0 === '0') {
            if (['', '-'].includes(str1)) return 'cell-bg1'; // 新增
            if (['', '-'].includes(str2)) return 'cell-bg3'; // 删除
            else return 'cell-bg2'; // 变更
          }
        }
        if (column.field === 'field1') {
          return 'filed-first3';
        }
      },
      columns: [
        { field: 'field1', title: '', width: '112px' },
        { field: 'field2', title: '会议人数', width: '112px' },
        { field: 'field3', title: '会议对接人', width: '200px' },
        { field: 'field4', title: '会议类型' },
        { field: 'field5', title: '会议估算', width: '160px' },
        { field: 'field6', title: '需求时间', width: '220px' },
      ],
      data: [],
    },
  },
  {
    title: '酒店需求',
    cardWidth: '80%',
    gridOptions: {
      border: 'full',
      cellClassName({ row, column }) {
        let resStyle = 'full-border ';
        if (['field4', 'field5'].includes(column.field)) {
          let str1 = row[column.field];
          let str2 =
            row[
              column.field.slice(0, column.field.length - 1) +
                (column.field.slice(column.field.length - 1, column.field.length) - 2)
            ];
          if (str1 != str2 && selCollapseS.selCollapse0 === '0') {
            if (['', '-'].includes(str1)) return 'cell-bg3';
            if (['', '-'].includes(str2)) return 'cell-bg1';
            else return 'cell-bg2';
          }
        }
        if (column.field === 'field1') {
          resStyle += 'filed-first2';
        }
        return resStyle;
      },
      columns: [
        { field: 'field1', title: '', width: '112px' },
        { field: 'field2', title: '原需求' },
        { field: 'field3', title: '' },
        { field: 'field4', title: '互动后新数据' },
        { field: 'field5', title: '' },
      ],
      data: [],
    },
  },
  {
    title: '每日计划',
    gridOptions: {
      border: 'full',
      cellClassName({ row, column, rowIndex, columnIndex }) {
        let resStyle = 'full-border ';
        if (columnIndex > 0 && columnIndex % 2 == 0) {
          let str1 = row[column.field];
          let str2 = row[column.field.slice(0, column.field.length - 1) + 'o'];
          if (str1 && str2 && str1 !== str2 && selCollapseS.selCollapse0 === '0') {
            if (['', '-'].includes(str1)) return 'cell-bg3';
            if (['', '-'].includes(str2)) return 'cell-bg1';
            else return 'cell-bg2';
          }
        }
        if (column.field === 'field1' && row.type !== 'bg1') {
          resStyle += 'filed-first';
        }
        if (row.type !== 'bg1') {
          if (!row[column.field]) row[column.field] = '-';
        }
        if (row.field1 === '附件' && columnIndex > 0) {
          return 'filed-first4';
        }
        return resStyle;
      },
      rowClassName({ row }) {
        if (row.type === 'bg1') {
          return 'row-bg1';
        }
        return null;
      },

      columns: [{ field: 'field1', width: '140px', type: 'html', title: '', minWidth: '100px' }],
      data: [],
    },
  },
  {
    cardWidth: '50%',
    title: '布展物料',
    gridOptions: {
      border: 'full',
      cellClassName({ row, column, rowIndex }) {
        let resStyle = 'full-border ';
        if (['field3'].includes(column.field)) {
          let str1 = row[column.field];
          let str2 =
            row[
              column.field.slice(0, column.field.length - 1) +
                (column.field.slice(column.field.length - 1, column.field.length) - 1)
            ];
          if (str1 !== str2 && selCollapseS.selCollapse0 === '0') {
            if (['', '-'].includes(str1)) return 'cell-bg3';
            if (['', '-'].includes(str2)) return 'cell-bg1';
            else return 'cell-bg2';
          }
        }
        if (column.field === 'field1' && row.type !== 'bg1') {
          resStyle += 'filed-first';
        }
        return resStyle;
      },
      rowClassName({ row }) {
        if (row.type === 'bg1') {
          return 'row-bg1';
        }
        return null;
      },
      columns: [
        { field: 'field1', width: '140px', type: 'html', title: '' },
        { field: 'field2', title: '原需求' },
        { field: 'field3', title: '互动后' },
      ],
      data: [],
    },
  },
  {
    cardWidth: '50%',
    title: '礼品需求',
    gridOptions: {
      border: 'full',
      cellClassName({ row, column, rowIndex }) {
        let resStyle = 'full-border ';
        if (['field3'].includes(column.field)) {
          let str1 = row[column.field];
          let str2 =
            row[
              column.field.slice(0, column.field.length - 1) +
                (column.field.slice(column.field.length - 1, column.field.length) - 1)
            ];
          if (str1 !== str2 && selCollapseS.selCollapse0 === '0') {
            if (['', '-'].includes(str1)) return 'cell-bg3';
            if (['', '-'].includes(str2)) return 'cell-bg1';
            else return 'cell-bg2';
          }
        }
        if (column.field === 'field1' && row.type !== 'bg1') {
          resStyle += 'filed-first';
        }
        return resStyle;
      },
      rowClassName({ row }) {
        if (row.type === 'bg1') {
          return 'row-bg1';
        }
        return null;
      },
      columns: [
        { field: 'field1', width: '140px', type: 'html', title: '' },
        { field: 'field2', title: '原需求' },
        { field: 'field3', title: '互动后' },
      ],
      data: [],
    },
  },
  {
    cardWidth: '50%',
    title: '其他需求',
    gridOptions: {
      border: 'full',
      cellClassName({ row, column, rowIndex }) {
        let resStyle = 'full-border ';
        if (['field3'].includes(column.field)) {
          let str1 = row[column.field];
          let str2 =
            row[
              column.field.slice(0, column.field.length - 1) +
                (column.field.slice(column.field.length - 1, column.field.length) - 1)
            ];
          if (str1 !== str2 && selCollapseS.selCollapse0 === '0') {
            if (['', '-'].includes(str1)) return 'cell-bg3';
            if (['', '-'].includes(str2)) return 'cell-bg1';
            else return 'cell-bg2';
          }
        }
        if (column.field === 'field1' && row.type !== 'bg1') {
          resStyle += 'filed-first';
        }
        return resStyle;
      },
      rowClassName({ row }) {
        if (row.type === 'bg1') {
          return 'row-bg1';
        }
        return null;
      },
      columns: [
        { field: 'field1', width: '140px', type: 'html', title: '' },
        { field: 'field2', title: '原需求' },
        { field: 'field3', title: '互动后' },
      ],
      data: [],
    },
  },
]);

const loading = ref<Boolean>(false);

const selCollapseS = reactive<SelCollapseS>({
  selCollapse0: '0',
  selCollapse1: '1',
  selCollapse2: '1',
  selCollapse3: '1',
  selCollapse4: '1',
  selCollapse5: '1',
  selCollapse6: '1',
});

const deepClone = (obj: object) => {
  if (obj === null || typeof obj !== 'object') return obj;
  let copy = Array.isArray(obj) ? [] : {};
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      copy[key] = deepClone(obj[key]);
    }
  }
  return copy;
};
const processId = ref('');
const arrDate = ref<string[]>([]);
const intentionConsultantUserCode = ref<string>('');
const getList = async () => {
  let res1 = {};
  if (props.previewSource === 'demandOne') {
    selCollapseS.selCollapse0 = '1';
  }

  // 地址栏取参
  const record = resolveParam(route.query.record);
  orderSource.value = record.orderSource;

  hideBtn.value = record.hideBtn || '';
  previewCalc.value = record.previewCalc || ''; // 是否预览价格
  demandRejectReason.value = record.finalReverseReason || '';
  // console.log('%c [ record ]-393', 'font-size:13px; background:pink; color:#bf2c9f;', record);

  if (props.previewSource === 'demandFromUser' || record?.processNode === 'DEMAND_PRE_INTERACT') {
    // DEMAND_PRE_INTERACT - 需求事先交互

    if (record?.processNode !== 'DEMAND_PRE_INTERACT') {
      // selCollapse0 - 0,对比  1,原始需求  2,互动需求
      selCollapseS.selCollapse0 = '1';
    }

    // 缓存取参
    const resCacheStr = await getDataBy({
      applicationCode: 'haierbusiness-mice-bid',
      cacheKey:
        'haierbusiness-mice-bid_' + loginUser.value?.username + '_demandSubKey' + (route.query.miceId || record.miceId), // 需求提报
    });
    if (resCacheStr) {
      res1 = JSON.parse(resCacheStr);
      console.log('%c [ 缓存取参-res1 ]-409', 'font-size:13px; background:pink; color:#bf2c9f;', res1);
    }
  }

  if (Object.keys(res1).length === 0) {
    // 详情取参
    if (props.platformType === 'merchant') {
      // 服务商端
      res1 = await schemeApi.demandOrderDetails({
        miceId: route.query.miceId || record.miceId,
      });
    } else if (props.platformType === 'manage') {
      // 平台端
      res1 = await miceBidManOrderListApi.platformDetails({
        miceId: route.query.miceId || record.miceId,
      });
    } else {
      // 用户端
      res1 = await miceBidManOrderListApi.userDetails({
        miceId: route.query.miceId || record.miceId,
      });
    }

    emit('hotelListEmit', res1.hotels);
  }

  intentionConsultantUserCode.value = res1.intentionConsultantUserCode;

  if (res1.processNode === res1.reverseProcessNode || res1.processNode === res1.reverseAfterProcessNode) {
    demandRejectReason.value = res1.demandRejectReason || res1.finalReverseReason; // 驳回原因
  }

  Object.assign(orderDetail, res1);
  let res = res1;
  processId.value = res1.pdMainId || '';
  // 需求互动 - 根据是否有sourceId
  if (res1.sourceId && props.previewSource === 'demandContrast') {
    if (props.platformType === 'manage') {
      res = await miceBidManOrderListApi.platformDetails({
        miceId: route.query.miceId || record.miceId,
        miceDemandId: res1.sourceId,
      });
    } else {
      res = await miceBidManOrderListApi.userDetails({
        miceId: route.query.miceId || record.miceId,
        miceDemandId: res1.sourceId,
      });
    }
  } else if (record?.processNode === 'DEMAND_PRE_INTERACT') {
    if (props.platformType === 'manage') {
      // 需求互动 - 详情取参
      res = await miceBidManOrderListApi.platformDetails({
        miceId: route.query.miceId || record.miceId,
      });
    } else {
      // 需求互动 - 详情取参
      res = await miceBidManOrderListApi.userDetails({
        miceId: route.query.miceId || record.miceId,
      });
    }
  }

  miceDetail.mainCode = res1.mainCode;
  miceDetail.miceName = res1.miceName;
  miceDetail.personTotal = res1.personTotal;
  miceDetail.miceType = res1.miceType;
  miceDetail.startDate = res1.startDate;
  miceDetail.endDate = res1.endDate;

  oldRes.value = JSON.stringify(res);
  newRes.value = JSON.stringify(res1);

  dataHandle(oldRes.value, newRes.value);
};
// 数据处理
const dataHandle = async (oldRes, newRes) => {
  const res = JSON.parse(oldRes);
  const res1 = JSON.parse(newRes);

  // 基本需求
  configList[0].gridOptions.data = [
    {
      id: 10001,
      field1: '原始需求',
      field2: dealData('0', res.personTotal),
      field3: dealData('1', res.contactUserName, res.contactUserCode),
      field4: dealData('2', res.miceType),
      field5: dealData('0', res.demandTotalPrice, '', '元'),
      field6: dealData('4', res.startDate, res.endDate),
    },
    {
      id: 10002,
      field1: '互动需求',
      field2: dealData('0', res1.personTotal),
      field3: dealData('1', res1.contactUserName, res1.contactUserCode),
      field4: dealData('2', res1.miceType),
      field5: dealData('0', res1.demandTotalPrice, '', '元'),
      field6: dealData('4', res1.startDate, res1.endDate),
    },
  ];

  configList[1].gridOptions.data = [];
  configList[2].gridOptions.data = [];
  configList[3].gridOptions.data = [];
  configList[4].gridOptions.data = [];
  configList[5].gridOptions.data = [];

  // 酒店需求
  if (props.merchantType === 0 || props.merchantType === 1 || props.merchantType === 2) {
    // selCollapseS.selCollapse0 - 0,对比  1,原始需求  2,互动需求
    const length_hotel =
      selCollapseS.selCollapse0 === '0'
        ? Math.max(res.hotels?.length || 0, res1.hotels?.length || 0)
        : selCollapseS.selCollapse0 === '1'
        ? res.hotels?.length || 0
        : res1.hotels?.length || 0;

    for (let i = 0; i < length_hotel; i++) {
      configList[1].gridOptions.data.push({
        id: i,
        field1: '酒店' + (i + 1),
        field2: dealData(
          '8',
          res.hotels[i]?.centerMarker
            ? res.hotels[i]?.centerMarker
            : res.hotels[i]?.provinceName + res.hotels[i]?.cityName + res.hotels[i]?.districtNames,
        ),
        field3: dealData('3', res.hotels[i]?.level),
        field4: dealData(
          '8',
          res1.hotels[i]?.centerMarker
            ? res1.hotels[i]?.centerMarker
            : res1.hotels[i]?.provinceName + res1.hotels[i]?.cityName + res1.hotels[i]?.districtNames,
        ),
        field5: dealData('3', res1.hotels[i]?.level),
      });
    }
    // if (length_hotel > 0)
    //   configList[1].gridOptions.data.push({
    //     id: 999,
    //     field1: '备注',
    //     field2: res.remarks,
    //     field4: res1.remarks,
    //   });
  }

  // 日期
  let set1 = new Set();
  let arrList = ['stays', 'places', 'caterings', 'vehicles', 'attendants', 'activities', 'insurances'];
  arrList.forEach((item) => {
    if (res[item]?.length > 0) {
      res[item].forEach((item1) => {
        set1.add(item1.demandDate);
      });
    }
    if (res1[item]?.length > 0) {
      res1[item].forEach((item1) => {
        set1.add(item1.demandDate);
      });
    }
  });
  let arrCol = [];
  configList[2].gridOptions.columns = configList[2].gridOptions.columns.slice(0, 1);

  // 所有日期
  arrDate.value = [...set1];
  for (let i = 0; i < arrDate.value.length; i++) {
    arrCol = arrCol.concat([
      { field: 'field' + arrDate.value[i] + 'o', title: arrDate.value[i] + ' 原需求', type: 'html', minWidth: '150px' },
      { field: 'field' + arrDate.value[i] + 'n', title: arrDate.value[i] + ' 互动后', type: 'html', minWidth: '150px' },
    ]);
  }
  configList[2].gridOptions.columns = configList[2].gridOptions.columns.concat(arrCol);

  if (props.merchantType === 0 || props.merchantType === 1 || props.merchantType === 2) {
    //
    // 住宿
    //
    // 按照日期区分新老数组
    const staysList = arrDate.value.map((e) => {
      return {
        oldArr: res.stays?.filter((j) => j.demandDate === e),
        newArr: res1.stays?.filter((j) => j.demandDate === e),
      };
    });
    // 取数组数量最大值
    const staysLengthArr = staysList.map((e) => {
      return Math.max(e.oldArr?.length, e.newArr?.length);
    });

    // selCollapseS.selCollapse0 - 0,对比  1,原始需求  2,互动需求
    const staysMax =
      selCollapseS.selCollapse0 === '0'
        ? Math.max(...staysLengthArr)
        : selCollapseS.selCollapse0 === '1'
        ? Math.max(
            ...staysList.map((e) => {
              return e.oldArr?.length || 0;
            }),
          )
        : Math.max(
            ...staysList.map((e) => {
              return e.newArr?.length || 0;
            }),
          );

    for (let i = 0; i < staysMax; i++) {
      let arr = [
        {
          id: 10001,
          field1: `<div style='font-size:14px;display:inline-block;vertical-align:middle'><img width='14' style='margin:0 10px' src='${demand_stay}'>住宿${
            i + 1
          }</div>`,
          type: 'bg1',
        },
        {
          id: 100010,
          field1: '日期',
        },
        {
          id: 100011,
          field1: '酒店选择',
        },
        {
          id: 100012,
          field1: '房型',
        },
        {
          id: 100013,
          field1: '人数',
        },
        {
          id: 100014,
          field1: '房间数',
        },
        {
          id: 100015,
          field1: '不一致原因',
        },
      ];

      // 住宿 - 是否预览价格
      if (previewCalc.value === 'previewCalc') {
        arr[7] = {
          id: 100016,
          field1: '费用预测',
        };
      }

      // 住宿内容-赋值
      for (let j = 0; j < arrDate.value.length; j++) {
        const oldData = staysList[j]?.oldArr[i];
        const newData = staysList[j]?.newArr[i];

        if (oldData?.demandDate || newData?.demandDate) {
          // 日期
          arr[1]['field' + arrDate.value[j] + 'o'] = dealData('8', oldData?.demandDate);
          arr[1]['field' + arrDate.value[j] + 'n'] = dealData('8', newData?.demandDate);
          // 酒店选择
          arr[2]['field' + arrDate.value[j] + 'o'] = dealData('6', oldData, res.hotels);
          arr[2]['field' + arrDate.value[j] + 'n'] = dealData('6', newData, res1.hotels);
          // 房型
          arr[3]['field' + arrDate.value[j] + 'o'] = dealData('5', oldData?.roomType, oldData?.breakfastType);
          arr[3]['field' + arrDate.value[j] + 'n'] = dealData('5', newData?.roomType, newData?.breakfastType);
          // 人数
          arr[4]['field' + arrDate.value[j] + 'o'] = dealData('0', oldData?.personNum);
          arr[4]['field' + arrDate.value[j] + 'n'] = dealData('0', newData?.personNum);
          // 房间数
          arr[5]['field' + arrDate.value[j] + 'o'] = dealData('0', oldData?.roomNum, '', '间夜');
          arr[5]['field' + arrDate.value[j] + 'n'] = dealData('0', newData?.roomNum, '', '间夜');
          // 不一致原因
          arr[6]['field' + arrDate.value[j] + 'o'] = dealData('8', oldData?.discrepancyReason);
          arr[6]['field' + arrDate.value[j] + 'n'] = dealData('8', newData?.discrepancyReason);

          // 住宿 - 费用预测
          if (previewCalc.value === 'previewCalc') {
            arr[7]['field' + arrDate.value[j] + 'o'] = dealData(
              '8',
              oldData?.calcUnitPrice && oldData?.roomNum
                ? oldData?.calcUnitPrice +
                    '(单价)*' +
                    oldData?.roomNum +
                    '间夜=' +
                    oldData?.calcUnitPrice * oldData?.roomNum +
                    '元'
                : '-',
            );
            arr[7]['field' + arrDate.value[j] + 'n'] = dealData(
              '8',
              newData?.calcUnitPrice && newData?.roomNum
                ? newData?.calcUnitPrice +
                    '(单价)*' +
                    newData?.roomNum +
                    '间夜=' +
                    newData?.calcUnitPrice * newData?.roomNum +
                    '元'
                : '-',
            );
          }
        }
      }

      configList[2].gridOptions.data = configList[2].gridOptions.data.concat(arr);
    }

    //
    // 会场
    //
    // 按照日期区分新老数组
    const placesList = arrDate.value.map((e) => {
      return {
        oldArr: res.places?.filter((j) => j.demandDate === e),
        newArr: res1.places?.filter((j) => j.demandDate === e),
      };
    });
    // 取数组数量最大值
    const placesLengthArr = placesList.map((e) => {
      return Math.max(e.oldArr?.length, e.newArr?.length);
    });

    // selCollapseS.selCollapse0 - 0,对比  1,原始需求  2,互动需求
    const placesMax =
      selCollapseS.selCollapse0 === '0'
        ? Math.max(...placesLengthArr)
        : selCollapseS.selCollapse0 === '1'
        ? Math.max(
            ...placesList.map((e) => {
              return e.oldArr?.length || 0;
            }),
          )
        : Math.max(
            ...placesList.map((e) => {
              return e.newArr?.length || 0;
            }),
          );

    for (let i = 0; i < placesMax; i++) {
      let arr = [
        {
          id: 10002,
          field1: `<div style='font-size:14px;display:inline-block;vertical-align:middle'><img width='14' style='margin:0 10px' src='${demand_place}'>会场${
            i + 1
          }</div>`,
          type: 'bg1',
        },
        {
          id: 100020,
          field1: '日期',
        },
        {
          id: 100021,
          field1: '酒店选择',
        },
        {
          id: 100022,
          field1: '使用时间',
        },
        {
          id: 100023,
          field1: '会场用途',
        },
        {
          id: 100024,
          field1: '人数',
        },
        {
          id: 100025,
          field1: '摆台形式',
        },
        {
          id: 100026,
          field1: '面积',
        },
        {
          id: 100027,
          field1: '层高',
        },
        {
          id: 100028,
          field1: 'LED数量',
        },
        {
          id: 100029,
          field1: 'LED规格描述',
        },
        {
          id: 1000210,
          field1: '茶歇标准',
        },
        {
          id: 1000211,
          field1: '茶歇说明',
        },
      ];

      // 会场 - 是否预览价格
      if (previewCalc.value === 'previewCalc') {
        arr[13] = {
          id: 1000212,
          field1: '费用预测',
        };
      }

      // 会场内容-赋值
      for (let j = 0; j < arrDate.value.length; j++) {
        const oldData = placesList[j]?.oldArr[i];
        const newData = placesList[j]?.newArr[i];

        if (oldData?.demandDate || newData?.demandDate) {
          // 日期
          arr[1]['field' + arrDate.value[j] + 'o'] = dealData('8', oldData?.demandDate);
          arr[1]['field' + arrDate.value[j] + 'n'] = dealData('8', newData?.demandDate);
          // 酒店选择
          arr[2]['field' + arrDate.value[j] + 'o'] = dealData('6', oldData, res.hotels);
          arr[2]['field' + arrDate.value[j] + 'n'] = dealData('6', newData, res1.hotels);
          // 使用时间
          arr[3]['field' + arrDate.value[j] + 'o'] = dealData('3.1', oldData?.usageTime);
          arr[3]['field' + arrDate.value[j] + 'n'] = dealData('3.1', newData?.usageTime);
          // 会场用途
          arr[4]['field' + arrDate.value[j] + 'o'] = dealData('3.2', oldData?.usagePurpose);
          arr[4]['field' + arrDate.value[j] + 'n'] = dealData('3.2', newData?.usagePurpose);
          // 人数
          arr[5]['field' + arrDate.value[j] + 'o'] = dealData('0', oldData?.personNum);
          arr[5]['field' + arrDate.value[j] + 'n'] = dealData('0', newData?.personNum);
          // 摆台形式
          arr[6]['field' + arrDate.value[j] + 'o'] = dealData('3.4', oldData?.tableType);
          arr[6]['field' + arrDate.value[j] + 'n'] = dealData('3.4', newData?.tableType);
          // 面积
          arr[7]['field' + arrDate.value[j] + 'o'] = dealData('0', oldData?.area, '', 'm²');
          arr[7]['field' + arrDate.value[j] + 'n'] = dealData('0', newData?.area, '', 'm²');
          // 层高
          arr[8]['field' + arrDate.value[j] + 'o'] = dealData('0', oldData?.underLightFloor, '', 'm');
          arr[8]['field' + arrDate.value[j] + 'n'] = dealData('0', newData?.underLightFloor, '', 'm');
          // LED数量
          arr[9]['field' + arrDate.value[j] + 'o'] = dealData('8', oldData?.ledNum);
          arr[9]['field' + arrDate.value[j] + 'n'] = dealData('8', newData?.ledNum);
          // LED规格描述
          arr[10]['field' + arrDate.value[j] + 'o'] = dealData('8', oldData?.ledSpecs);
          arr[10]['field' + arrDate.value[j] + 'n'] = dealData('8', newData?.ledSpecs);
          // 茶歇标准
          arr[11]['field' + arrDate.value[j] + 'o'] = dealData('0', oldData?.teaEachTotalPrice, '', '元/位');
          arr[11]['field' + arrDate.value[j] + 'n'] = dealData('0', newData?.teaEachTotalPrice, '', '元/位');
          // 茶歇说明
          arr[12]['field' + arrDate.value[j] + 'o'] = dealData('8', oldData?.teaDesc);
          arr[12]['field' + arrDate.value[j] + 'n'] = dealData('8', newData?.teaDesc);

          // 会场 - 费用预测
          if (previewCalc.value === 'previewCalc') {
            let oldPrice = oldData?.calcUnitPlacePrice || 0;
            // 单价*LED数量
            if (oldData?.calcUnitLedPrice && oldData?.ledNum) {
              oldPrice = oldPrice + oldData?.calcUnitLedPrice * oldData?.ledNum;
            }
            // 茶歇单价*会场人数
            if (oldData?.calcUnitTeaPrice && oldData?.personNum) {
              oldPrice = oldPrice + oldData?.calcUnitTeaPrice * oldData?.personNum;
            }

            let newPrice = newData?.calcUnitPlacePrice || 0;
            // 单价*LED数量
            if (newData?.calcUnitLedPrice && newData?.ledNum) {
              newPrice = newPrice + newData?.calcUnitLedPrice * newData?.ledNum;
            }
            // 茶歇单价*会场人数
            if (newData?.calcUnitTeaPrice && newData?.personNum) {
              newPrice = newPrice + newData?.calcUnitTeaPrice * newData?.personNum;
            }

            arr[13]['field' + arrDate.value[j] + 'o'] = dealData(
              '8',
              oldData?.calcUnitPlacePrice && !oldData?.hasLed && !oldData?.hasTea
                ? oldPrice + '元'
                : oldData?.calcUnitPlacePrice
                ? oldData?.calcUnitPlacePrice +
                  '(单价)' +
                  (oldData?.calcUnitLedPrice && oldData?.ledNum
                    ? '+' + oldData?.calcUnitLedPrice + '*' + oldData?.ledNum + '+'
                    : '') +
                  (oldData?.calcUnitTeaPrice && oldData?.personNum
                    ? '+' + oldData?.calcUnitTeaPrice + '*' + oldData?.personNum
                    : '') +
                  '=' +
                  oldPrice +
                  '元'
                : '-',
            );
            arr[13]['field' + arrDate.value[j] + 'n'] = dealData(
              '8',
              newData?.calcUnitPlacePrice && !newData?.hasLed && !newData?.hasTea
                ? newPrice + '元'
                : newData?.calcUnitPlacePrice
                ? newData?.calcUnitPlacePrice +
                  '(单价)' +
                  (newData?.calcUnitLedPrice && newData?.ledNum
                    ? '+' + newData?.calcUnitLedPrice + '*' + newData?.ledNum + '+'
                    : '') +
                  (newData?.calcUnitTeaPrice && newData?.personNum
                    ? '+' + newData?.calcUnitTeaPrice + '*' + newData?.personNum
                    : '') +
                  '=' +
                  newPrice +
                  '元'
                : '-',
            );
          }
        }
      }

      configList[2].gridOptions.data = configList[2].gridOptions.data.concat(arr);
    }

    //
    //  用餐
    //
    // 按照日期区分新老数组
    const cateringsList = arrDate.value.map((e) => {
      return {
        oldArr: res.caterings?.filter((j) => j.demandDate === e),
        newArr: res1.caterings?.filter((j) => j.demandDate === e),
      };
    });
    // 取数组数量最大值
    const cateringsLengthArr = cateringsList.map((e) => {
      return Math.max(e.oldArr?.length, e.newArr?.length);
    });

    // selCollapseS.selCollapse0 - 0,对比  1,原始需求  2,互动需求
    const cateringsMax =
      selCollapseS.selCollapse0 === '0'
        ? Math.max(...cateringsLengthArr)
        : selCollapseS.selCollapse0 === '1'
        ? Math.max(
            ...cateringsList.map((e) => {
              return e.oldArr?.length || 0;
            }),
          )
        : Math.max(
            ...cateringsList.map((e) => {
              return e.newArr?.length || 0;
            }),
          );

    for (let i = 0; i < cateringsMax; i++) {
      let arr = [
        {
          id: 10003,
          field1: `<div style='font-size:14px;display:inline-block;vertical-align:middle'><img width='14' style='margin:0 10px' src='${demand_catering}'>用餐${
            i + 1
          }</div>`,
          type: 'bg1',
        },
        {
          id: 100030,
          field1: '日期',
        },
        {
          id: 100031,
          field1: '餐饮提供方',
        },
        {
          id: 100032,
          field1: '酒店位置',
        },
        {
          id: 100033,
          field1: '用餐类型',
        },
        {
          id: 100034,
          field1: '用餐时间',
        },
        {
          id: 100035,
          field1: '人数',
        },
        {
          id: 100036,
          field1: '餐标',
        },
        {
          id: 100037,
          field1: '是否包含酒水',
        },
      ];

      // 用餐 - 是否预览价格
      if (previewCalc.value === 'previewCalc') {
        arr[9] = {
          id: 100038,
          field1: '费用预测',
        };
      }

      // 用餐内容-赋值
      for (let j = 0; j < arrDate.value.length; j++) {
        const oldData = cateringsList[j]?.oldArr[i];
        const newData = cateringsList[j]?.newArr[i];

        if (oldData?.demandDate || newData?.demandDate) {
          // 日期
          arr[1]['field' + arrDate.value[j] + 'o'] = dealData('8', oldData?.demandDate);
          arr[1]['field' + arrDate.value[j] + 'n'] = dealData('8', newData?.demandDate);
          // 餐饮提供方
          arr[2]['field' + arrDate.value[j] + 'o'] = dealData('3.5', oldData?.isInsideHotel);
          arr[2]['field' + arrDate.value[j] + 'n'] = dealData('3.5', newData?.isInsideHotel);
          // 酒店位置
          // 非酒店内用餐，隐藏酒店位置
          arr[3]['field' + arrDate.value[j] + 'o'] = oldData?.isInsideHotel ? dealData('6', oldData, res.hotels) : '-';
          arr[3]['field' + arrDate.value[j] + 'n'] = newData?.isInsideHotel ? dealData('6', newData, res1.hotels) : '-';
          // 用餐类型
          arr[4]['field' + arrDate.value[j] + 'o'] = dealData('3.6', oldData?.cateringType);
          arr[4]['field' + arrDate.value[j] + 'n'] = dealData('3.6', newData?.cateringType);
          // 用餐时间
          arr[5]['field' + arrDate.value[j] + 'o'] = dealData('3.7', oldData?.cateringTime);
          arr[5]['field' + arrDate.value[j] + 'n'] = dealData('3.7', newData?.cateringTime);
          // 人数
          arr[6]['field' + arrDate.value[j] + 'o'] = dealData('0', oldData?.personNum);
          arr[6]['field' + arrDate.value[j] + 'n'] = dealData('0', newData?.personNum);
          // 餐标
          arr[7]['field' + arrDate.value[j] + 'o'] = dealData('0', oldData?.demandUnitPrice, '', '元/位');
          arr[7]['field' + arrDate.value[j] + 'n'] = dealData('0', newData?.demandUnitPrice, '', '元/位');
          // 是否包含酒水
          arr[8]['field' + arrDate.value[j] + 'o'] = dealData('3.8', oldData?.isIncludeDrinks);
          arr[8]['field' + arrDate.value[j] + 'n'] = dealData('3.8', newData?.isIncludeDrinks);

          // 用餐 - 费用预测
          if (previewCalc.value === 'previewCalc') {
            arr[9]['field' + arrDate.value[j] + 'o'] = dealData(
              '8',
              oldData?.calcUnitPrice && oldData?.personNum
                ? oldData?.calcUnitPrice +
                    '*' +
                    oldData?.personNum +
                    '=' +
                    oldData?.calcUnitPrice * oldData?.personNum +
                    '元'
                : '-',
            );
            arr[9]['field' + arrDate.value[j] + 'n'] = dealData(
              '8',
              newData?.calcUnitPrice && newData?.personNum
                ? newData?.calcUnitPrice +
                    '*' +
                    newData?.personNum +
                    '=' +
                    newData?.calcUnitPrice * newData?.personNum +
                    '元'
                : '-',
            );
          }
        }
      }

      configList[2].gridOptions.data = configList[2].gridOptions.data.concat(arr);
    }
  }

  //
  // 用车
  //
  if (props.merchantType === 0 || props.merchantType === 1 || props.merchantType === 2 || props.merchantType === 5) {
    // 按照日期区分新老数组
    const vehiclesList = arrDate.value.map((e) => {
      return {
        oldArr: res.vehicles?.filter((j) => j.demandDate === e),
        newArr: res1.vehicles?.filter((j) => j.demandDate === e),
      };
    });
    // 取数组数量最大值
    const vehiclesLengthArr = vehiclesList.map((e) => {
      return Math.max(e.oldArr?.length, e.newArr?.length);
    });

    // selCollapseS.selCollapse0 - 0,对比  1,原始需求  2,互动需求
    const vehiclesMax =
      selCollapseS.selCollapse0 === '0'
        ? Math.max(...vehiclesLengthArr)
        : selCollapseS.selCollapse0 === '1'
        ? Math.max(
            ...vehiclesList.map((e) => {
              return e.oldArr?.length || 0;
            }),
          )
        : Math.max(
            ...vehiclesList.map((e) => {
              return e.newArr?.length || 0;
            }),
          );

    for (let i = 0; i < vehiclesMax; i++) {
      let arr = [
        {
          id: 10004,
          field1: `<div style='font-size:14px;display:inline-block;vertical-align:middle'><img width='14' style='margin:0 10px' src='${demand_vehicle}'>用车${
            i + 1
          }</div>`,
          type: 'bg1',
        },
        {
          id: 100040,
          field1: '日期',
        },
        {
          id: 100041,
          field1: '用车方式',
        },
        {
          id: 100042,
          field1: '车型',
        },
        {
          id: 100043,
          field1: '型号',
        },
        {
          id: 100044,
          field1: '用车数量',
        },
        {
          id: 100045,
          field1: '使用时长',
        },
        {
          id: 100046,
          field1: '路线',
        },
        {
          id: 100047,
          field1: '路线概述',
        },
      ];

      // 用车 - 是否预览价格
      if (previewCalc.value === 'previewCalc') {
        arr[9] = {
          id: 100048,
          field1: '费用预测',
        };
      }

      // 用车内容-赋值
      for (let j = 0; j < arrDate.value.length; j++) {
        const oldData = vehiclesList[j]?.oldArr[i];
        const newData = vehiclesList[j]?.newArr[i];

        if (oldData?.demandDate || newData?.demandDate) {
          // 日期
          arr[1]['field' + arrDate.value[j] + 'o'] = dealData('8', oldData?.demandDate);
          arr[1]['field' + arrDate.value[j] + 'n'] = dealData('8', newData?.demandDate);
          // 用车方式
          arr[2]['field' + arrDate.value[j] + 'o'] = dealData('3.9', oldData?.usageType);
          arr[2]['field' + arrDate.value[j] + 'n'] = dealData('3.9', newData?.usageType);
          // 车型
          arr[3]['field' + arrDate.value[j] + 'o'] = dealData('3.10', oldData?.seats);
          arr[3]['field' + arrDate.value[j] + 'n'] = dealData('3.10', newData?.seats);
          // 型号
          arr[4]['field' + arrDate.value[j] + 'o'] = dealData('3.11', oldData?.brand);
          arr[4]['field' + arrDate.value[j] + 'n'] = dealData('3.11', newData?.brand);
          // 用车数量
          arr[5]['field' + arrDate.value[j] + 'o'] = dealData('0', oldData?.vehicleNum, '', '辆');
          arr[5]['field' + arrDate.value[j] + 'n'] = dealData('0', newData?.vehicleNum, '', '辆');
          // 使用时长
          arr[6]['field' + arrDate.value[j] + 'o'] =
            oldData?.usageType !== undefined && oldData?.usageType !== null && oldData?.usageType == 1
              ? dealData('3.12', oldData?.usageTime)
              : '-';

          arr[6]['field' + arrDate.value[j] + 'n'] =
            newData?.usageType !== undefined && newData?.usageType !== null && newData?.usageType == 1
              ? dealData('3.12', newData?.usageTime)
              : '-';
          // 路线
          arr[7]['field' + arrDate.value[j] + 'o'] =
            oldData?.usageType !== undefined && oldData?.usageType !== null && oldData?.usageType == 0
              ? dealData('10', oldData?.route)
              : '-';
          arr[7]['field' + arrDate.value[j] + 'n'] =
            newData?.usageType !== undefined && newData?.usageType !== null && newData?.usageType == 0
              ? dealData('10', newData?.route)
              : '-';
          // 路线概述
          arr[8]['field' + arrDate.value[j] + 'o'] =
            oldData?.usageType !== undefined && oldData?.usageType !== null && oldData?.usageType == 0
              ? '-'
              : dealData('8', oldData?.route);
          arr[8]['field' + arrDate.value[j] + 'n'] =
            newData?.usageType !== undefined && newData?.usageType !== null && newData?.usageType == 0
              ? '-'
              : dealData('8', newData?.route);

          // 用车 - 费用预测
          if (previewCalc.value === 'previewCalc') {
            arr[9]['field' + arrDate.value[j] + 'o'] = dealData(
              '8',
              oldData?.calcUnitPrice && oldData?.vehicleNum
                ? oldData?.calcUnitPrice +
                    '*' +
                    oldData?.vehicleNum +
                    '=' +
                    oldData?.calcUnitPrice * oldData?.vehicleNum +
                    '元'
                : '-',
            );
            arr[9]['field' + arrDate.value[j] + 'n'] = dealData(
              '8',
              newData?.calcUnitPrice && newData?.vehicleNum
                ? newData?.calcUnitPrice +
                    '*' +
                    newData?.vehicleNum +
                    '=' +
                    newData?.calcUnitPrice * newData?.vehicleNum +
                    '元'
                : '-',
            );
          }
        }
      }

      configList[2].gridOptions.data = configList[2].gridOptions.data.concat(arr);
    }
  }

  //
  // 服务人员
  //
  if (props.merchantType === 0 || props.merchantType === 1 || props.merchantType === 2) {
    // 按照日期区分新老数组
    const attendantsList = arrDate.value.map((e) => {
      return {
        oldArr: res.attendants?.filter((j) => j.demandDate === e),
        newArr: res1.attendants?.filter((j) => j.demandDate === e),
      };
    });
    // 取数组数量最大值
    const attendantsLengthArr = attendantsList.map((e) => {
      return Math.max(e.oldArr?.length, e.newArr?.length);
    });

    // selCollapseS.selCollapse0 - 0,对比  1,原始需求  2,互动需求
    const attendantsMax =
      selCollapseS.selCollapse0 === '0'
        ? Math.max(...attendantsLengthArr)
        : selCollapseS.selCollapse0 === '1'
        ? Math.max(
            ...attendantsList.map((e) => {
              return e.oldArr?.length || 0;
            }),
          )
        : Math.max(
            ...attendantsList.map((e) => {
              return e.newArr?.length || 0;
            }),
          );

    for (let i = 0; i < attendantsMax; i++) {
      let arr = [
        {
          id: 10005,
          field1: `<div style='font-size:14px;display:inline-block;vertical-align:middle'><img width='14' style='margin:0 10px' src='${demand_attendant}'>服务人员${
            i + 1
          }</div>`,
          type: 'bg1',
        },
        {
          id: 100050,
          field1: '日期',
        },
        {
          id: 100051,
          field1: '人员类型',
        },
        {
          id: 100052,
          field1: '人数',
        },
        {
          id: 100053,
          field1: '工作范围',
        },
      ];

      // 服务人员 - 是否预览价格
      if (previewCalc.value === 'previewCalc') {
        arr[5] = {
          id: 100054,
          field1: '费用预测',
        };
      }

      // 服务人员内容-赋值
      for (let j = 0; j < arrDate.value.length; j++) {
        const oldData = attendantsList[j]?.oldArr[i];
        const newData = attendantsList[j]?.newArr[i];

        if (oldData?.demandDate || newData?.demandDate) {
          // 日期
          arr[1]['field' + arrDate.value[j] + 'o'] = dealData('8', oldData?.demandDate);
          arr[1]['field' + arrDate.value[j] + 'n'] = dealData('8', newData?.demandDate);
          // 人员类型
          arr[2]['field' + arrDate.value[j] + 'o'] = dealData('3.13', oldData?.type);
          arr[2]['field' + arrDate.value[j] + 'n'] = dealData('3.13', newData?.type);
          // 人数
          arr[3]['field' + arrDate.value[j] + 'o'] = dealData('8', oldData?.personNum);
          arr[3]['field' + arrDate.value[j] + 'n'] = dealData('8', newData?.personNum);
          // 工作范围
          arr[4]['field' + arrDate.value[j] + 'o'] = dealData('8', oldData?.duty);
          arr[4]['field' + arrDate.value[j] + 'n'] = dealData('8', newData?.duty);

          // 服务人员 - 费用预测
          if (previewCalc.value === 'previewCalc') {
            arr[5]['field' + arrDate.value[j] + 'o'] = dealData(
              '8',
              oldData?.calcUnitPrice && oldData?.personNum
                ? oldData?.calcUnitPrice +
                    '*' +
                    oldData?.personNum +
                    '=' +
                    oldData?.calcUnitPrice * oldData?.personNum +
                    '元'
                : '-',
            );
            arr[5]['field' + arrDate.value[j] + 'n'] = dealData(
              '8',
              newData?.calcUnitPrice && newData?.personNum
                ? newData?.calcUnitPrice +
                    '*' +
                    newData?.personNum +
                    '=' +
                    newData?.calcUnitPrice * newData?.personNum +
                    '元'
                : '-',
            );
          }
        }
      }

      configList[2].gridOptions.data = configList[2].gridOptions.data.concat(arr);
    }

    //
    // 拓展活动
    //
    // 按照日期区分新老数组
    const activitiesList = arrDate.value.map((e) => {
      return {
        oldArr: res.activities?.filter((j) => j.demandDate === e),
        newArr: res1.activities?.filter((j) => j.demandDate === e),
      };
    });
    // 取数组数量最大值
    const activitiesLengthArr = activitiesList.map((e) => {
      return Math.max(e.oldArr?.length, e.newArr?.length);
    });

    // selCollapseS.selCollapse0 - 0,对比  1,原始需求  2,互动需求
    const activitiesMax =
      selCollapseS.selCollapse0 === '0'
        ? Math.max(...activitiesLengthArr)
        : selCollapseS.selCollapse0 === '1'
        ? Math.max(
            ...activitiesList.map((e) => {
              return e.oldArr?.length || 0;
            }),
          )
        : Math.max(
            ...activitiesList.map((e) => {
              return e.newArr?.length || 0;
            }),
          );

    for (let i = 0; i < activitiesMax; i++) {
      let arr = [
        {
          id: 10006,
          field1: `<div style='font-size:14px;display:inline-block;vertical-align:middle'><img width='14' style='margin:0 10px' src='${demand_activity}'>拓展活动${
            i + 1
          }</div>`,
          type: 'bg1',
        },
        {
          id: 100060,
          field1: '日期',
        },
        {
          id: 100061,
          field1: '费用标准',
        },
        {
          id: 100062,
          field1: '人数',
        },
        {
          id: 100063,
          field1: '活动需求',
        },
        {
          id: 100064,
          field1: '附件',
        },
      ];

      // 拓展活动 - 是否预览价格
      if (previewCalc.value === 'previewCalc') {
        arr[6] = {
          id: 100065,
          field1: '费用预测',
        };
      }

      // 拓展活动内容-赋值
      for (let j = 0; j < arrDate.value.length; j++) {
        const oldData = activitiesList[j]?.oldArr[i];
        const newData = activitiesList[j]?.newArr[i];

        if (oldData?.demandDate || newData?.demandDate) {
          // 日期
          arr[1]['field' + arrDate.value[j] + 'o'] = dealData('8', oldData?.demandDate);
          arr[1]['field' + arrDate.value[j] + 'n'] = dealData('8', newData?.demandDate);
          // 费用标准
          arr[2]['field' + arrDate.value[j] + 'o'] = dealData('0', oldData?.demandUnitPrice, '', '元/人');
          arr[2]['field' + arrDate.value[j] + 'n'] = dealData('0', newData?.demandUnitPrice, '', '元/人');
          // 人数
          arr[3]['field' + arrDate.value[j] + 'o'] = dealData('8', oldData?.personNum);
          arr[3]['field' + arrDate.value[j] + 'n'] = dealData('8', newData?.personNum);
          // 活动需求
          arr[4]['field' + arrDate.value[j] + 'o'] = dealData('8', oldData?.description);
          arr[4]['field' + arrDate.value[j] + 'n'] = dealData('8', newData?.description);
          // 附件
          arr[5]['field' + arrDate.value[j] + 'o'] = dealData('9', oldData?.paths);
          arr[5]['field' + arrDate.value[j] + 'n'] = dealData('9', newData?.paths);

          // 拓展活动 - 费用预测
          if (previewCalc.value === 'previewCalc') {
            arr[6]['field' + arrDate.value[j] + 'o'] = dealData(
              '8',
              oldData?.calcUnitPrice && oldData?.personNum
                ? oldData?.calcUnitPrice +
                    '*' +
                    oldData?.personNum +
                    '=' +
                    oldData?.calcUnitPrice * oldData?.personNum +
                    '元'
                : '-',
            );
            arr[6]['field' + arrDate.value[j] + 'n'] = dealData(
              '8',
              newData?.calcUnitPrice && newData?.personNum
                ? newData?.calcUnitPrice +
                    '*' +
                    newData?.personNum +
                    '=' +
                    newData?.calcUnitPrice * newData?.personNum +
                    '元'
                : '-',
            );
          }
        }
      }

      configList[2].gridOptions.data = configList[2].gridOptions.data.concat(arr);
    }
  }

  //
  // 保险 - TODO
  //
  if (props.merchantType === 0 || props.merchantType === 3) {
    // 按照日期区分新老数组
    const insurancesList = arrDate.value.map((e) => {
      return {
        oldArr: res.insurances?.filter((j) => j.demandDate === e),
        newArr: res1.insurances?.filter((j) => j.demandDate === e),
      };
    });
    // 取数组数量最大值
    const insurancesLengthArr = insurancesList.map((e) => {
      return Math.max(e.oldArr?.length, e.newArr?.length);
    });

    // selCollapseS.selCollapse0 - 0,对比  1,原始需求  2,互动需求
    const insurancesMax =
      selCollapseS.selCollapse0 === '0'
        ? Math.max(...insurancesLengthArr)
        : selCollapseS.selCollapse0 === '1'
        ? Math.max(
            ...insurancesList.map((e) => {
              return e.oldArr?.length || 0;
            }),
          )
        : Math.max(
            ...insurancesList.map((e) => {
              return e.newArr?.length || 0;
            }),
          );

    for (let i = 0; i < insurancesMax; i++) {
      let arr = [
        {
          id: 10007,
          field1: `<div style='font-size:14px;display:inline-block;vertical-align:middle'><img width='14' style='margin:0 10px' src='${demand_insurance}'>保险${
            i + 1
          }</div>`,
          type: 'bg1',
        },
        {
          id: 100070,
          field1: '日期',
        },
        {
          id: 100071,
          field1: '参保人数',
        },
        {
          id: 100072,
          field1: '保险产品',
        },
      ];

      // 保险 - 是否预览价格
      if (previewCalc.value === 'previewCalc') {
        arr[4] = {
          id: 100073,
          field1: '费用预测',
        };
      }

      // 保险内容-赋值
      for (let j = 0; j < arrDate.value.length; j++) {
        const oldData = insurancesList[j]?.oldArr[i];
        const newData = insurancesList[j]?.newArr[i];

        if (oldData?.demandDate || newData?.demandDate) {
          // 日期
          arr[1]['field' + arrDate.value[j] + 'o'] = dealData('8', oldData?.demandDate);
          arr[1]['field' + arrDate.value[j] + 'n'] = dealData('8', newData?.demandDate);
          // 参保人数
          arr[2]['field' + arrDate.value[j] + 'o'] = dealData('8', oldData?.personNum);
          arr[2]['field' + arrDate.value[j] + 'n'] = dealData('8', newData?.personNum);
          // 保险产品
          arr[3]['field' + arrDate.value[j] + 'o'] = dealData('8', oldData?.insuranceName);
          arr[3]['field' + arrDate.value[j] + 'n'] = dealData('8', newData?.insuranceName);

          // 保险 - 费用预测
          if (previewCalc.value === 'previewCalc') {
            arr[4]['field' + arrDate.value[j] + 'o'] = dealData(
              '8',
              oldData?.calcUnitPrice && oldData?.personNum
                ? oldData?.calcUnitPrice +
                    '*' +
                    oldData?.personNum +
                    '=' +
                    oldData?.calcUnitPrice * oldData?.personNum +
                    '元'
                : '-',
            );
            arr[4]['field' + arrDate.value[j] + 'n'] = dealData(
              '8',
              newData?.calcUnitPrice && newData?.personNum
                ? newData?.calcUnitPrice +
                    '*' +
                    newData?.personNum +
                    '=' +
                    newData?.calcUnitPrice * newData?.personNum +
                    '元'
                : '-',
            );
          }
        }
      }

      configList[2].gridOptions.data = configList[2].gridOptions.data.concat(arr);
    }
  }

  if (props.merchantType === 0 || props.merchantType === 1 || props.merchantType === 2) {
    // 布展物料
    if (
      (res.material && Object.keys(res.material).length > 0) ||
      (res1.material && Object.keys(res1.material).length > 0)
    ) {
      const detailsList = res.material?.materialDetails || [];
      const detailsList1 = res1.material?.materialDetails || [];

      const length_material = Math.max(detailsList?.length || 0, detailsList1?.length || 0);

      for (let i = 0; i < length_material; i++) {
        let arr = [
          {
            id: 1000 + i,
            field1: `<div style='font-size:14px;display:inline-block;vertical-align:middle'><img width='14' style='margin:0 10px' src='${demand_material}'>布展物料${
              i + 1
            }</div>`,
            field2: '',
            field3: '',
            field4: '',
            field5: '',
            field6: '',
            type: 'bg1',
          },
          {
            id: 1000 + i + 1,
            field1: '物料类型',
            field2: dealData('3.3', detailsList[i]?.type),
            field3: dealData('3.3', detailsList1[i]?.type),
          },
          {
            id: 1000 + i + 2,
            field1: '规格说明',
            field2: dealData('8', detailsList[i]?.specs),
            field3: dealData('8', detailsList1[i]?.specs),
          },
          {
            id: 1000 + i + 3,
            field1: '数量',
            field2: dealData('8', detailsList[i]?.num),
            field3: dealData('8', detailsList1[i]?.num),
          },
          {
            id: 1000 + i + 4,
            field1: '单价',
            field2: dealData('0', detailsList[i]?.unitPrice, '', '元'),
            field3: dealData('0', detailsList1[i]?.unitPrice, '', '元'),
          },
          {
            id: 1000 + i + 5,
            field1: '总预算',
            field2: dealData('0', detailsList[i]?.num * detailsList[i]?.unitPrice, '', '元'),
            field3: dealData('0', detailsList1[i]?.num * detailsList1[i]?.unitPrice, '', '元'),
          },
        ];

        configList[3].gridOptions.data = configList[3].gridOptions.data.concat(arr);
      }
    }
  }

  // 票务预定 - TODO
  // demand_traffic

  // 礼品需求
  if (props.merchantType === 0 || props.merchantType === 4) {
    const length_presents = Math.max(res.presents?.length || 0, res1.presents?.length || 0);
    for (let i = 0; i < length_presents; i++) {
      let arr = [
        {
          id: 2000 + i,
          field1: `<div style='font-size:14px;display:inline-block;vertical-align:middle'><img width='14' style='margin:0 10px' src='${demand_present}'>礼品${
            i + 1
          }</div>`,
          field2: '',
          field3: '',
          field4: '',
          field5: '',
          field6: '',
          type: 'bg1',
        },
        {
          id: 2000 + i + 1,
          field1: '礼品类型',
          field2: dealData('8', res.presents[i]?.productName),
          field3: dealData('8', res1.presents[i]?.productName),
        },
        {
          id: 2000 + i + 2,
          field1: '数量',
          field2: dealData('8', res.presents[i]?.personNum),
          field3: dealData('8', res1.presents[i]?.personNum),
        },
        {
          id: 2000 + i + 2,
          field1: '单位',
          field2: dealData('8', res.presents[i]?.unit),
          field3: dealData('8', res1.presents[i]?.unit),
        },
        {
          id: 2000 + i + 3,
          field1: '总预算',
          field2: dealData('0', res.presents[i]?.demandTotalPrice, '', '元'),
          field3: dealData('0', res1.presents[i]?.demandTotalPrice, '', '元'),
        },
        {
          id: 2000 + i + 4,
          field1: '送达日期',
          field2: dealData('8', res.presents[i]?.deliveryDate),
          field3: dealData('8', res1.presents[i]?.deliveryDate),
        },
        {
          id: 2000 + i + 5,
          field1: '礼品描述',
          field2: dealData('8', res.presents[i]?.personSpecs),
          field3: dealData('8', res1.presents[i]?.personSpecs),
        },
      ];
      configList[4].gridOptions.data = configList[4].gridOptions.data.concat(arr);
    }
  }

  if (props.merchantType === 0 || props.merchantType === 1 || props.merchantType === 2) {
    // 其他需求
    const length_others = Math.max(res.others?.length || 0, res1.others?.length || 0);
    for (let i = 0; i < length_others; i++) {
      let arr = [
        {
          id: 3000 + i,
          field1: `<div style='font-size:14px;display:inline-block;vertical-align:middle'><img width='14' style='margin:0 10px' src='${demand_other}'>其他需求${
            i + 1
          }</div>`,
          field2: '',
          field3: '',
          field4: '',
          field5: '',
          field6: '',
          type: 'bg1',
        },
        {
          id: 3000 + i + 1,
          field1: '项目',
          field2: dealData('8', res.others[i]?.itemName),
          field3: dealData('8', res1.others[i]?.itemName),
        },
        {
          id: 3000 + i + 2,
          field1: '数量',
          field2: dealData('8', res.others[i]?.num),
          field3: dealData('8', res1.others[i]?.num),
        },
        {
          id: 3000 + i + 2,
          field1: '单位',
          field2: dealData('8', res.others[i]?.unit),
          field3: dealData('8', res1.others[i]?.unit),
        },
        {
          id: 3000 + i + 3,
          field1: '总预算',
          field2: dealData('0', res.others[i]?.demandTotalPrice, '', '元'),
          field3: dealData('0', res1.others[i]?.demandTotalPrice, '', '元'),
        },
        {
          id: 3000 + i + 4,
          field1: '规则说明',
          field2: dealData('8', res.others[i]?.specs),
          field3: dealData('8', res1.others[i]?.specs),
        },
      ];
      configList[5].gridOptions.data = configList[5].gridOptions.data.concat(arr);
    }
  }

  loading.value = true;

  configListC();
};
const widthSearch = ref('80%');

// 分开查看
const changeWatch = (val?: object, type?: string) => {
  loading.value = false;
  if (type === 'val') {
    // console.log(val);
  } else {
    if (selCollapseS.selCollapse0 === '0') selCollapseS.selCollapse0 = '1';
    else selCollapseS.selCollapse0 = '0';
  }
  loading.value = true;

  dataHandle(oldRes.value, newRes.value);
  configListC();
};
const dealData = (
  type?: string,
  data1?: object | string | number,
  data2?: object | string | number,
  unit?: object | string,
) => {
  if (data1 || data1 === 0 || data1 === false) {
    if (type === '0') return data1 + (unit || '人');
    else if (type === '1') return data1 + ' ' + data2;
    else if (type === '2') return MiceTypeConstantO.ofType(data1).desc;
    else if (type === '3') {
      // 酒店-星级
      return hotelLevelAllConstant.ofType(data1)?.desc || '-';
    } else if (type === '3.1') {
      // 会场-使用时间
      return PlaceUsageTimeTypeConstant.ofType(data1)?.desc || '-';
    } else if (type === '3.2') {
      // 会场用途
      return UsagePurposeTypeConstant.ofType(data1)?.desc || '-';
    } else if (type === '3.3') {
      // 物料类型
      return MaterialTypeConstant.ofType(data1)?.desc || '-';
    } else if (type === '3.4') {
      // 摆台形式
      return TableTypeConstant.ofType(data1)?.desc || '-';
    } else if (type === '3.5') {
      // 是否酒店内用餐
      return HotelDinnerTypeConstant.ofType(typeof data1 == 'number' ? data1 : data1 ? 1 : 0)?.desc || '-';
    } else if (type === '3.6') {
      // 用餐类型
      return CateringTypeConstant.ofType(data1)?.desc || '-';
    } else if (type === '3.7') {
      // 用餐时间
      return CateringTimeTypeConstant.ofType(data1)?.desc || '-';
    } else if (type === '3.8') {
      // 是否包含酒水
      return HaveDrinksTypeConstant.ofType(data1)?.desc || '-';
    } else if (type === '3.9') {
      // 用车方式
      return CarUsageTypeConstant.ofType(data1)?.desc || '-';
    } else if (type === '3.10') {
      // 型号
      return SeatTypeConstant.ofType(data1)?.desc || '-';
    } else if (type === '3.11') {
      // 车型
      return CarBrandTypeConstant.ofType(data1)?.desc || '-';
    } else if (type === '3.12') {
      // 使用时长
      return UsageTimeTypeConstant.ofType(data1)?.desc || '-';
    } else if (type === '3.13') {
      // 人员类型
      return AttendantTypeConstant.ofType(data1)?.desc || '-';
    } else if (type === '4')
      return data1.split(' ')[0].replaceAll('-', '/') + '-' + data2.split(' ')[0].replaceAll('-', '/');
    else if (type === '5') {
      const str1 = RoomTypeConstant.ofType(data1)?.desc || '-';
      const str2 = BreakfastTypeConstant.ofType(data2)?.desc || '-';
      return str1 + '/' + str2;
    } else if (type === '6') {
      let str = '-';

      data2.forEach((item, index) => {
        if (
          (item.tempDemandHotelId && item.tempDemandHotelId === data1.tempDemandHotelId) ||
          (item.id && item.id === data1.miceDemandHotelId)
        ) {
          // 需求提报-预览
          str = `酒店${index + 1}(${
            (item.centerMarker ? item.centerMarker : item.provinceName + item.cityName + item.districtNames) +
            '/' +
            dealData('3', item.level)
          })`;
        }
      });
      return str;
    } else if (type === '8') {
      return data1;
    } else if (type === '9') {
      if (data1.length > 0) {
        let str = '';
        let document = {};
        data1.forEach((item, index) => {
          try {
            document = JSON.parse(item);
          } catch (error) {
            console.log(error);
          }

          str += `<a target='_blank' href='${document.url}'>${
            document.name
          }</a><span style='margin-right: 10px;color: #86909c' >${index === data1.length - 1 ? '' : ','}</span>`;
        });
        return str;
      }
      return '-';
    } else if (type === '10') {
      if (data1.length > 0) {
        const arr = data1.split(',') || [];
        const str = arr.join('-');
        return str;
      }
      return '-';
    }
  } else {
    return '-';
  }
};
defineExpose({
  intentionConsultantUserCode,
  processId,
  orderDetail,
});
onMounted(() => {
  let element = document.getElementById('myElement')?.offsetWidth + 20 + 'px';
  widthSearch.value = element || '80%';
  window.addEventListener('resize', function () {
    let element = document.getElementById('myElement')?.offsetWidth + 20 + 'px';
    widthSearch.value = element || '80%';
  });
  getList();
});
</script>
<template>
  <!-- 需求预览-需求对比 -->
  <div class="demand_contrast_container" id="myElement">
    <a-alert
      v-if="demandRejectReason"
      class="mb16 demand_reject_reason"
      message="驳回原因："
      :description="demandRejectReason"
      show-icon
      type="warning"
    />
    <div class="demand_contrast_header demand_contrast_flex">
      <div class="demand_contrast_left">
        <div class="interact_mice_title">
          <div class="interact_mice_name_img mr8"></div>
          <div class="interact_mice_name mr24">
            {{ miceDetail.miceName || '' }}
          </div>
        </div>
        <div class="interact_mice_num mt12">
          <span class="mr10">会议编号：{{ miceDetail.mainCode }}</span>
          <img @click="getCopy(miceDetail.mainCode)" src="@/assets/image/scheme/copy_blue.png" width="16" />
        </div>

        <a-row class="interact_mice_info mt24">
          <a-col :span="6">
            <span class="mice_info_title mice_info_person_img">会议人数：</span>
            <span class="mice_info_value">
              {{ miceDetail.personTotal ? miceDetail.personTotal + '人' : '-' }}
            </span>
          </a-col>
          <a-col :span="6">
            <span class="mice_info_title mice_info_type_img">会议类型：</span>
            <span class="mice_info_value">
              {{ MiceTypeConstant.ofType(miceDetail.miceType)?.desc || '' }}
            </span>
          </a-col>

          <a-col :span="6">
            <span class="mice_info_title mice_info_time_img">需求开始时间：</span>
            <span class="mice_info_value">
              {{ miceDetail.startDate || '' }}
            </span>
          </a-col>
          <a-col :span="6">
            <span class="mice_info_title mice_info_time_img">需求结束时间：</span>
            <span class="mice_info_value">
              {{ miceDetail.endDate || '' }}
            </span>
          </a-col>
          <a-row class="interact_mice_info mt24">
            <a-col :span="6" v-if="oldRes.remarks">
              <span class="mice_info_title mice_info_time_img">备注：</span>
              <span class="mice_info_value">
                {{ oldRes.remarks || '' }}
              </span>
            </a-col>
          </a-row>
        </a-row>

        <div class="demand_contrast_title3" v-show="selCollapseS.selCollapse0 === '0'">
          标注：
          <div class="demand_contrast_icon1"></div>
          新增需求
          <div class="demand_contrast_icon2"></div>
          变更需求
          <div class="demand_contrast_icon3"></div>
          删除需求
        </div>
      </div>
      <div class="demand_contrast_right" style="display: flex; align-items: center">
        <slot name="header"></slot>
      </div>
    </div>
    <div class="demand_process_slot">
      <slot name="processSlot"></slot>
    </div>
    <div v-if="loading">
      <a-collapse
        v-for="(item, index) in configObj.arr"
        :key="item.id"
        v-show="item.show != false"
        collapsible="icon"
        v-model:activeKey="selCollapseS['selCollapse' + (index + 1)]"
        ghost
      >
        <template #expandIcon="{ isActive }">
          <div
            class="demand_contrast_collapse_arrow"
            :style="{ transform: 'rotate(' + (isActive ? 0 : 180) + 'deg)' }"
          ></div>
        </template>
        <a-collapse-panel
          v-if="item.gridOptions.data.length > 0"
          class="demand_contrast_card_item"
          key="1"
          :header="item.title"
        >
          <VisibleTable
            v-if="loading"
            :key="'table-' + index + '-' + selCollapseS.selCollapse0"
            :gridOptions="item.gridOptions"
            :style="'width:' + item.cardWidth"
          /> </a-collapse-panel
      ></a-collapse>
    </div>

    <div
      :class="
        'demand_contrast_footer ' +
        (hideBtn == '1' || useAttrs().orderSource == 'user' || orderSource == 'user' ? 'footer-user-width' : '')
      "
      :style="`width:${widthSearch}`"
    >
      <div>
        <div
          class="demand_contrast_footer_center"
          v-show="selCollapseS.selCollapse0 !== '0' && props.previewSource === 'demandContrast'"
        >
          <a-radio-group
            @change="(e) => changeWatch(e, 'val')"
            v-model:value="selCollapseS.selCollapse0"
            name="radioGroup"
          >
            <a-radio value="1">原始需求</a-radio>
            <a-radio value="2">互动需求</a-radio>
          </a-radio-group>
        </div>
        <Button
          size="small"
          v-show="props.previewSource === 'demandContrast'"
          @click="changeWatch()"
          style="margin-right: 10px"
          >{{ selCollapseS.selCollapse0 === '0' ? '分开查看' : '对比查看' }}</Button
        >
        <slot name="footer"></slot>
      </div>
    </div>
  </div>
</template>
<style scoped lang="less">
.demand_contrast_container {
  position: relative;
  width: 100%;
  padding-bottom: 60px;

  .demand_contrast_flex {
    display: flex;
    justify-content: space-between;
  }
  .demand_contrast_header {
    min-height: 50px;
    width: 100%;
    padding: 24px 32px;

    background: url('@/assets/image/scheme/mice_bgc.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .demand_contrast_left {
    width: 70%;

    .interact_mice_title {
      display: flex;
      align-items: center;

      .interact_mice_name_img {
        width: 28px;
        height: 28px;
        background-image: url('@/assets/image/scheme/mice_name.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
      }

      .interact_mice_name {
        font-family: PingFangSCSemibold, PingFangSCSemibold;
        font-weight: normal;
        font-size: 20px;
        color: #1d2129;
        line-height: 28px;
      }

      .interact_mice_type {
        width: 108px;
        height: 28px;
        line-height: 28px;
        text-align: center;

        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #ffffff;
        background: #1868db;
        border-radius: 4px;
      }
    }

    .interact_mice_num {
      display: flex;
      align-items: center;

      font-size: 14px;
      color: #86909c;
      line-height: 20px;

      img {
        cursor: pointer;
      }
    }
    .interact_mice_info {
      width: 100%;
      font-size: 14px;
      color: #86909c;
      line-height: 20px;

      .mice_info_title {
        display: inline-block;
        text-indent: 26px;
        background-size: 16px 16px;
        background-repeat: no-repeat;
        background-position: center left;
      }
      .mice_info_person_img {
        background-image: url('@/assets/image/scheme/mice_person.png');
      }

      .mice_info_type_img {
        background-image: url('@/assets/image/scheme/mice_type.png');
      }

      .mice_info_time_img {
        background-image: url('@/assets/image/scheme/mice_time.png');
      }

      .mice_info_value {
        color: #1d2129;
      }
    }
  }

  .demand_contrast_title3 {
    min-width: 400px;
    white-space: nowrap;
    padding-left: 20px;
    margin-top: 20px;
    width: 40%;
    height: 40px;
    background: #f2f3f5;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #1d2129;
    line-height: 40px;
    text-align: left;
    font-style: normal;
    display: flex;
    align-items: center;
  }
  .demand_contrast_icon1 {
    margin: 0 10px 0 20px;
    width: 18px;
    height: 18px;
    border: 1px solid #52c41a;
    background: #f1faef;
    display: inline-block;
  }
  .demand_contrast_icon2 {
    margin: 0 10px 0 20px;
    width: 18px;
    height: 18px;
    border: 1px solid #fcc04c;
    background: #fffaf1;
    display: inline-block;
  }
  .demand_contrast_icon3 {
    margin: 0 10px 0 20px;
    width: 18px;
    height: 18px;
    border: 1px solid #fe7b62;
    background: #fff1f0;
    display: inline-block;
  }
  :deep(.demand_contrast_card_item) {
    margin-top: 10px;
    width: 100%;
    background: #ffffff;
    .ant-collapse-header-text {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 18px;
      color: #1d2129;
      line-height: 25px;
      text-align: left;
      font-style: normal;
    }
  }
  .demand_contrast_footer {
    z-index: 10;
    position: fixed;
    bottom: 0;
    right: 0;
    padding: 10px 20px;
    margin-top: 10px;
    width: calc(100% - 250px);
    text-align: right;
    background: #ffffff;
    border-top: 1px solid #f1f2f6;
    box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.03);
  }
  .wid100 {
    width: 100% !important;
  }
  .footer-user-width {
    width: 1280px !important;
    left: calc(50% - 640px);
  }
  .demand_contrast_footer_center {
    display: inline-block;
    text-align: center;
  }
  :deep(.ant-collapse-header) {
    position: relative;
    font-weight: 500;
    font-size: 20px;
    color: #1d2129;
    line-height: 28px;
  }
  .demand_contrast_collapse_arrow {
    position: absolute;
    top: 15px;
    left: 0px;
    width: 4px;
    height: 20px;
    background: #1868db;
  }
  :deep(.ant-btn-default) {
    padding: 3px 8px;
    height: 32px;
    width: 80px;
    border-radius: 2px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #4e5969;
    line-height: 22px;
    text-align: center;
    font-style: normal;
  }

  :deep(.ant-btn-primary) {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 22px;
    text-align: center;
    font-style: normal;
    padding: 3px 8px;
    height: 32px;
    min-width: 80px;
    /* background: #1868db; */
    border-radius: 2px;
  }
}
.demand_reject_reason {
  padding: 24px;
  margin: 0 15px 16px;
  border-radius: 12px;
}
</style>
