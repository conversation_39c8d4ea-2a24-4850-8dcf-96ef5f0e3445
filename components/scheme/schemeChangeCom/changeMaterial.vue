<script setup lang="ts">
// 方案变更-布展物料
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';
import { MaterialTypeConstant, materialDetailsArr } from '@haierbusiness-front/common-libs';

const props = defineProps({
  schemeInfo: {
    type: Object,
    default: {},
  },
  schemeCacheInfo: {
    type: Object,
    default: {},
  },
  bargainSchemeInfo: {
    type: Object,
    default: {},
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  isSchemeBargain: {
    // 是否议价
    type: Boolean,
    default: false,
  },
  schemeChangeType: {
    // schemeBargainingEdit - 方案议价, schemeBargainingView - 议价查看, schemeChangeEdit - 方案变更, schemeChangeView - 方案变更查看, schemeWinBidView - 方案中标查看
    type: String,
    default: '',
  },
  processNode: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['materialPriceEmit', 'schemeMaterialEmit']);

const oldSchemeList = ref<array>([]);
const newSchemeList = ref<array>([]);

const materialInfo = ref({});

const subtotal = ref<number>(0); // 小计

const schemePlanLabelList = ['物料类型', '规格说明', '数量', '单位', '单价'];

const isVerifyFailed = ref<boolean>(false); // 校验是否失败
// 布展物料
const materialParams = ref<materialDetailsArr>({
  // 需求布展物料明细
  miceDemandMaterialId: null, // 需求布展物料表id
  type: null, // 物料类型
  specs: '', // 规格说明
  schemeMaterialNum: null, // 数量
  unit: '', // 单位
  schemeUnitPrice: null, // 单价
});

const changePrice = (index: number) => {
  // 小计
  subtotal.value = 0;
  newSchemeList.value.forEach((e) => {
    if (e.schemeUnitPrice && e.schemeMaterialNum) {
      subtotal.value += e.schemeUnitPrice * e.schemeMaterialNum;
    }
  });

  emit('materialPriceEmit', subtotal.value);
};

const addScheme = (idx: number) => {
  isVerifyFailed.value = false;

  newSchemeList.value.push({
    isSchemeChangeAdd: true, // 是否变更方案新增

    ...materialParams.value,
  });
};
const delScheme = (idx: number) => {
  newSchemeList.value.splice(idx, 1);
};

// 锚点
const anchorJump = (id: string) => {
  document.getElementById(id).scrollIntoView({ behavior: 'smooth', block: 'center' });
};

// 暂存
const materialTempSave = () => {
  emit('schemeMaterialEmit', {
    schemeMaterial: {
      miceDemandMaterialId: materialInfo.value?.id,
      demandTotalPrice: materialInfo.value?.demandTotalPrice,
      schemeTotalPrice: subtotal.value,
      // description: materialInfo.value?.description,
      materialDetails: [...newSchemeList.value],
    },
  });
};

// 校验
const materialSub = () => {
  let isVerPassed = true;

  newSchemeList.value.forEach((e, i) => {
    isVerifyFailed.value = true;

    if (isVerPassed === false) return;

    if (e.type === null || e.type === undefined) {
      message.error('请选择布展物料' + (i + 1) + '物料类型');

      isVerPassed = false;
      anchorJump('changeSchemeMaterialId' + i);
      return;
    }

    if (!e.specs) {
      message.error('请填写布展物料' + (i + 1) + '规格说明');

      isVerPassed = false;
      anchorJump('changeSchemeMaterialId' + i);
      return;
    }

    if (e.schemeMaterialNum === null || e.schemeMaterialNum === undefined) {
      message.error('请填写布展物料' + (i + 1) + '数量');

      isVerPassed = false;
      anchorJump('changeSchemeMaterialId' + i);
      return;
    }

    if (!e.unit && e.isSchemeChangeAdd) {
      message.error('请填写布展物料' + (i + 1) + '单位');

      isVerPassed = false;
      anchorJump('changeSchemeMaterialId' + i);
      return;
    }

    if (e.schemeUnitPrice === null || e.schemeUnitPrice === undefined) {
      message.error('请填写布展物料' + (i + 1) + '单价');

      isVerPassed = false;
      anchorJump('changeSchemeMaterialId' + i);
      return;
    }
  });

  if (isVerPassed) {
    materialTempSave();
  }

  return isVerPassed;
};

defineExpose({ materialSub, materialTempSave });

onMounted(async () => {
  // console.log(
  //   '%c [ 布展物料 ]-24',
  //   'font-size:13px; background:pink; color:#bf2c9f;',
  //   props.schemeInfo.material,
  //   props.schemeInfo.material?.materialDetails,
  // );

  if (
    (props.schemeInfo.material && props.schemeInfo.material.materialDetails) ||
    (props.schemeCacheInfo.material && props.schemeCacheInfo.material.materialDetails)
  ) {
    oldSchemeList.value = JSON.parse(JSON.stringify(props.schemeInfo))?.material?.materialDetails || [];

    if (props.isSchemeCache && props.schemeCacheInfo) {
      // 缓存 - 反显
      newSchemeList.value = props.schemeCacheInfo?.material?.materialDetails || [];

      materialInfo.value = props.schemeCacheInfo?.material || {};
    } else if (props.isSchemeBargain && props.bargainSchemeInfo) {
      // 议价、议价查看
      newSchemeList.value = props.bargainSchemeInfo?.material?.materialDetails || [];

      newSchemeList.value.forEach((e, index) => {
        e.schemeUnitPrice = e.schemeUnitPrice || oldSchemeList.value[index].schemeUnitPrice;
        e.schemeMaterialNum = e.schemeMaterialNum || oldSchemeList.value[index].schemeMaterialNum;
      });

      materialInfo.value = props.bargainSchemeInfo?.material || {};
    } else {
      // 变更查看
      newSchemeList.value = JSON.parse(JSON.stringify(oldSchemeList.value));

      materialInfo.value = JSON.parse(JSON.stringify(props.schemeInfo))?.material || {};
    }

    changePrice();
  }
});
</script>

<template>
  <!-- 布展物料 -->
  <div class="scheme_vehicle">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>布展物料</span>
    </div>

    <div class="common_table mt16">
      <!-- 左侧 -->
      <div class="common_table_l">
        <div class="scheme_plan_table" v-for="(item, idx) in oldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '布展物料' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ MaterialTypeConstant.ofType(item.type)?.desc || '-' }}
                </template>
                {{ MaterialTypeConstant.ofType(item.type)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.specs || '-' }}
                </template>
                {{ item.specs || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemeMaterialNum || '-' }}
                </template>
                {{ item.schemeMaterialNum || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.unit || '-' }}
                </template>
                {{ item.unit || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemeUnitPrice ? item.schemeUnitPrice + '元' : '-' }}
                </template>
                {{ item.schemeUnitPrice ? item.schemeUnitPrice + '元' : '-' }}
              </a-tooltip>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  item.schemeUnitPrice && item.schemeMaterialNum
                    ? '¥' + formatNumberThousands(item.schemeUnitPrice * item.schemeMaterialNum)
                    : '-'
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeUnitPrice && item.schemeMaterialNum">
                {{ item.schemeMaterialNum + '*' + item.schemeUnitPrice + '(单价)' }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div class="common_table_r">
        <div class="scheme_plan_table" v-for="(item, idx) in newSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '布展物料' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value" :id="'changeSchemeMaterialId' + idx">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.type === null || item.type === undefined) && item.isSchemeChangeAdd
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ MaterialTypeConstant.ofType(item.type)?.desc || '-' }}
                    </template>
                    {{ MaterialTypeConstant.ofType(item.type)?.desc || '-' }}
                  </a-tooltip>
                </div>

                <a-select
                  v-else
                  v-model:value="item.type"
                  style="width: 100%"
                  placeholder="请选择物料类型"
                  :bordered="false"
                  :dropdownMatchSelectWidth="90"
                  allow-clear
                >
                  <a-select-option v-for="item in MaterialTypeConstant.toArray()" :key="item.code" :value="item.code">
                    <a-tooltip placement="topLeft" :title="item.desc">
                      {{ item.desc }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div :class="['scheme_plan_border', 'p0', isVerifyFailed && !item.specs ? 'error_tip' : '']">
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.specs || '-' }}
                    </template>
                    {{ item.specs || '-' }}
                  </a-tooltip>
                </div>
                <div v-else>
                  <a-input
                    v-model:value="item.specs"
                    style="width: calc(100% - 30px)"
                    placeholder="请填写规格说明"
                    :bordered="false"
                    :maxlength="200"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.schemeMaterialNum === null || item.schemeMaterialNum === undefined)
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingView', 'schemeChangeView', 'schemeWinBidView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.schemeMaterialNum || '-' }}
                    </template>
                    {{ item.schemeMaterialNum || '-' }}
                  </a-tooltip>
                </div>

                <div class="pl12" v-else>
                  <a-input-number
                    style="width: calc(100% - 30px)"
                    v-model:value="item.schemeMaterialNum"
                    @blur="changePrice(idx)"
                    placeholder="请填写数量"
                    :bordered="false"
                    allow-clear
                    :min="0"
                    :max="999999"
                    :precision="0"
                  />
                  <span></span>
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div :class="['scheme_plan_border', 'p0', isVerifyFailed && !item.unit ? 'error_tip' : '']">
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.unit || '-' }}
                    </template>
                    {{ item.unit || '-' }}
                  </a-tooltip>
                </div>
                <div v-else>
                  <a-input
                    v-model:value="item.unit"
                    style="width: calc(100% - 30px)"
                    placeholder="请填写单位"
                    :bordered="false"
                    :maxlength="200"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.schemeUnitPrice === null || item.schemeUnitPrice === undefined)
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingView', 'schemeChangeView', 'schemeWinBidView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.schemeUnitPrice ? item.schemeUnitPrice + '元' : '-' }}
                    </template>
                    {{ item.schemeUnitPrice ? item.schemeUnitPrice + '元' : '-' }}
                  </a-tooltip>
                </div>

                <div class="pl12" v-else>
                  <a-input-number
                    style="width: calc(100% - 44px)"
                    v-model:value="item.schemeUnitPrice"
                    @blur="changePrice(idx)"
                    placeholder="请填写单价"
                    :bordered="false"
                    allow-clear
                    :min="0"
                    :max="999999"
                    :precision="0"
                  />
                  <span>元</span>
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  item.schemeUnitPrice && item.schemeMaterialNum
                    ? '¥' + formatNumberThousands(item.schemeUnitPrice * item.schemeMaterialNum)
                    : '-'
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeUnitPrice && item.schemeMaterialNum">
                {{ item.schemeMaterialNum + '*' + item.schemeUnitPrice + '(单价)' }}
              </div>
            </div>

            <!-- 操作 -->
            <div
              class="action_icons"
              v-if="
                ['MICE_PENDING', 'MICE_EXECUTION', 'MICE_COMPLETED'].includes(processNode) &&
                schemeChangeType === 'schemeChangeEdit' &&
                newSchemeList.length > 1 &&
                item.isSchemeChangeAdd
              "
            >
              <a-popconfirm
                :title="'确认删除布展物料' + (idx + 1) + '？'"
                placement="topRight"
                ok-text="确认"
                cancel-text="取消"
                @confirm="delScheme(idx)"
              >
                <div class="del_icon"></div>
              </a-popconfirm>
            </div>
          </div>
        </div>

        <div v-show="subtotal" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>

        <div
          v-if="
            ['MICE_PENDING', 'MICE_EXECUTION', 'MICE_COMPLETED'].includes(processNode) &&
            schemeChangeType === 'schemeChangeEdit'
          "
          class="add_scheme_plan mt20"
          @click="addScheme(index)"
        >
          <div class="plan_add_img mr8"></div>
          <span>新增布展物料</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_vehicle {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_place.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  .scheme_plan_value {
    :deep(.ant-input-number .ant-input-number-input) {
      height: 22px;
      padding: 0;
    }

    .scheme_plan_edit {
      margin-left: 5px;
      display: inline-flex;
      vertical-align: middle;

      width: 16px;
      height: 18px;
      background: url('@/assets/image/common/edit_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
    }
  }

  .scheme_plan_price_value {
    :deep(.ant-input-number .ant-input-number-input) {
      height: 24px;
      padding: 0 5px;
      text-align: end;

      width: 84px;
      font-weight: 500;
      font-size: 14px;
      color: #1868db;
      text-align: right;
      border-bottom: 1px solid #4e5969;
    }
  }

  .error_price_tip {
    :deep(.ant-input-number .ant-input-number-input) {
      border-bottom: 2px solid #ff4d4f;
    }
  }

  .p0 {
    padding: 0 !important;
  }
  .pr0 {
    padding-right: 0 !important;
  }

  .plan_add_btn {
    /* padding: 0 20px; */
    /* width: 108px;
    height: 24px; */
    /* border: 2px solid rgba(24, 104, 219, 0.2); */
    margin-top: 2px;
    border-radius: 2px;
    display: flex;
    align-items: center;

    font-weight: 400;
    font-size: 12px;
    color: #1868db;

    .plan_add_img {
      width: 12px;
      height: 12px;
      background: url('@/assets/image/demand/demand_add_blue.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }

  /* custom-antd.css */
  :deep(.ant-select-disabled .ant-select-selector) {
    color: rgba(134, 144, 156, 1);
  }
}
</style>
