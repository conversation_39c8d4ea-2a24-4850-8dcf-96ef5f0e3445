// vite.config.ts
import { loadEnv } from "file:///D:/groupGitCode/haierbusiness/haierbusiness-front/node_modules/.pnpm/vite@4.4.9_@types+node@18.17.11_less@4.2.0_sass@1.69.5/node_modules/vite/dist/node/index.js";
import vue from "file:///D:/groupGitCode/haierbusiness/haierbusiness-front/node_modules/.pnpm/@vitejs+plugin-vue@4.3.3_vite@4.4.9_vue@3.3.4/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import path from "path";
var __vite_injected_original_dirname = "D:\\groupGitCode\\haierbusiness\\haierbusiness-front\\projects\\haierbusiness-business-team-front";
var CWD = process.cwd();
var vite_config_default = ({ mode }) => {
  const { VITE_BASE_URL } = loadEnv(mode, CWD);
  // 生成版本信息插件
  const generateVersionPlugin = {
    name: 'generate-version',
    // 仅在构建时执行
    apply: 'build',
    closeBundle() {
      // 生成版本文件
      const versionInfo = {
        // 使用时间戳作为版本号
        version: Date.now(),
        buildTime: new Date().toISOString(),
        mode: mode
      };

      
            // 确保 dist 目录存在
            const distPath = path.resolve(__dirname, 'dist')
            if (!distPath) {
                mkdirSync(distPath, {recursive: true})
            }

            writeFileSync(
                path.resolve(__dirname, 'dist/version.json'),
                JSON.stringify(versionInfo, null, 2)
            );

    }
  };
  return {
    base: VITE_BASE_URL,
    resolve: {
      alias: {
        "~": path.resolve(__vite_injected_original_dirname, "./"),
        "@": path.resolve(__vite_injected_original_dirname, "./src")
      }
    },
    plugins: [vue(),generateVersionPlugin],
    build: {
      target: ["es2015"],
      sourcemap: true
    },
    server: {
      port: 5201,
      host: "0.0.0.0",
      proxy: {
        // http://************:9223/team/order/create
        // http://localhost:5201/hb/team/order/create
        // 本地开发
        // "/hb/team/api": {
        //   target: "http://************:9223",
        //   changeOrigin: true,
        //   rewrite: (path) => path.replace(new RegExp(`/hb/team/api`), ''),
        // },
        "/hb": {
          target: "https://businessmanagement-test.haier.net/hbweb/index/hb",
          changeOrigin: true,
          rewrite: (path2) => path2.replace(/^\/hb/, "")
        },
        "/upload": {
          target: "https://businessmanagement-test.haier.net/hbweb/upload",
          changeOrigin: true,
          rewrite: (path2) => path2.replace(/^\/upload/, "")
        }
      }
    }
    // css: {
    //   postcss: {
    //     plugins: [
    //       postcssPx2remExclude({
    //         remUnit: 192, 
    //         // exclude: /mobile/i // 忽略node_modules目录下的文件
    //       })
    //     ]
    //   }
    // }
  };
};
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
