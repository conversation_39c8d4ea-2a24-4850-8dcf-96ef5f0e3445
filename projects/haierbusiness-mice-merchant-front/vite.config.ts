import {loadEnv} from 'vite';

import vue from '@vitejs/plugin-vue';
import path from 'path';
import {mkdirSync, writeFileSync} from "fs";

const CWD = process.cwd();

export default ({mode}) => {
    const {VITE_BASE_URL} = loadEnv(mode, CWD);
    // 生成版本信息插件
    const generateVersionPlugin = {
        name: 'generate-version',
        // 仅在构建时执行
        apply: 'build',
        closeBundle() {
            // 生成版本文件
            const versionInfo = {
                // 使用时间戳作为版本号
                version: Date.now(),
                buildTime: new Date().toISOString(),
                mode: mode
            };


            // 确保 dist 目录存在
            const distPath = path.resolve(__dirname, 'dist')
            if (!distPath) {
                mkdirSync(distPath, {recursive: true})
            }

            writeFileSync(
                path.resolve(__dirname, 'dist/version.json'),
                JSON.stringify(versionInfo, null, 2)
            );

        }
    };
    return {
        base: VITE_BASE_URL,
        resolve: {
            alias: {
                '~': path.resolve(__dirname, './'),
                '@': path.resolve(__dirname, './src'),
            },
        },
        plugins: [vue(), generateVersionPlugin],
        build: {
            target: ['es2015'],
        },
        server: {
            port: 5160,
            host: true,
            proxy: {
                // '/hb/common/api': {
                //   // target: "http://localhost:8080/hb",
                //   //  target: "https://businessmanagement-test.haier.net/hbweb/payman/hb",
                //   target: 'http://localhost:9206',
                //   changeOrigin: true,
                //   rewrite: (path) => path.replace(/^\/hb\/common\/api/, ''),
                // },
                '/hb/mice-bid/api': {
                    target: 'https://businessmanagement-test.haier.net/hbweb/merchant/hb/mice-bid/api/',
                    // target: 'http://*************:9209/', //李靖
                    // target: 'http://*************:9209/', //贾博文
                    changeOrigin: true,
                    rewrite: (path) => path.replace(new RegExp(`/hb/mice-bid/api`), ''),
                },
                '/hb/merchant/api': {
                    target: 'https://businessmanagement-test.haier.net/hbweb/merchant/hb/merchant/api/',
                    changeOrigin: true,
                    rewrite: (path) => path.replace(new RegExp(`/hb/merchant/api`), ''),
                },
                '/hb': {
                    target: 'https://businessmanagement-test.haier.net/hbweb/index/hb',
                    changeOrigin: true,
                    rewrite: (path) => path.replace(/^\/hb/, ''),
                },
            },
        },
    };
};
