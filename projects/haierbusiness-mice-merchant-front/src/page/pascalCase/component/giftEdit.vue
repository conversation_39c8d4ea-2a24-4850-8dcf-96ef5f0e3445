<script lang="ts" setup>
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  CheckboxGroup as hCheckboxGroup,
  Checkbox as hCheckbox,
  Row as hRow,
  Col as hCol,
  DatePicker as hDatePicker,
  message,
  Upload as hUpload,
  RadioGroup as hRadioGroup,
  Radio as hRadio,
  InputNumber as hInputNumber,
  Button as hButton,
  Switch as hSwitch,
  Table as hTable,
  Divider as hDivider,
  Image as hImage,
  FormProps,
  TableColumnsType,
  TableColumnType,
} from 'ant-design-vue';
import { computed, ref, watch, onMounted, inject, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import type { Ref } from 'vue';
import { IPascalCase, IPriceTableItem, ISpecType, FileTypeConstant } from '@haierbusiness-front/common-libs';
import { fileApi, pascalCaseApi } from '@haierbusiness-front/apis';
import PlusOutlined from '@ant-design/icons-vue/PlusOutlined';
import LoadingOutlined from '@ant-design/icons-vue/LoadingOutlined';
import { UploadOutlined, CloseOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { toNumber } from 'lodash';
import router from '../../../router';
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil';

// const router = useRouter()
const route = useRoute();

const currentRouter = ref();
const isCloseLastTab = inject<Ref<boolean>>('isCloseLastTab');

const from = ref();
const confirmLoading = ref(false);
const id = ref<number>();
const mode = ref<string>('add'); // 新增模式: add, 编辑模式: edit, 查看模式: view
const sourceType = ref<any>(); // 保存sourceType字段

// 判断是否为查看模式
const isViewMode = computed(() => mode.value === 'view');

// 判断是否为编辑模式
const isEditMode = computed(() => mode.value === 'edit');

//图片查看
const previewVisible = ref(false);
let previewImage = ref('');
const previewTitle = ref('预览');
const previewImages = ref<string[]>([]);
const handleView = (imgs: string[]) => {
  if (imgs.length == 1) {
    imgs.splice(0, 1);
  } else {
    previewVisible.value = true;
    console.log(imgs, 'imgs');
    previewImages.value = imgs;
    previewImage.value = imgs[0];
  }
};
const modifyPicture = (img: string) => {
  previewImage.value = img;
};
const handleDelete = (index: number) => {
  console.log(index, 'index');
  console.log(previewImages.value[index]);
  previewImages.value.splice(index, 1);
  console.log(previewImages.value, 'previewImages.value');
  nextTick(() => {
    previewImage.value = previewImages.value[Math.max(0, index - 1)] || '';
  });
  console.log(previewImage.value, 'previewImage.value');
};

// 步骤控制
const currentStep = ref(0);

// 规格配置相关
const specTypes = ref<ISpecType[]>([
  {
    id: Date.now(),
    type: '',
    values: [''],
  },
]);

const priceTableData = ref<IPriceTableItem[]>([]);

// 价格配置表格列
const priceColumns: TableColumnsType = [
  {
    title: '规格名称',
    dataIndex: 'specName',
    key: 'specName',
    width: 180,
    align: 'center',
  },
  {
    title: 'skuId',
    dataIndex: 'skuId',
    key: 'skuId',
    width: 150,
    align: 'center',
  },
  {
    title: '图片上传',
    dataIndex: 'imageUrl',
    key: 'imageUrl',
    width: 120,
    align: 'center',
  },
  {
    title: '市场价(元)',
    dataIndex: 'marketPrice',
    key: 'marketPrice',
    width: 100,
    align: 'center',
  },
  {
    title: '成本价(元)',
    dataIndex: 'costPrice',
    key: 'costPrice',
    width: 100,
    align: 'center',
  },
  {
    title: '销售价(元)',
    dataIndex: 'salePrice',
    key: 'salePrice',
    width: 100,
    align: 'center',
  },
  {
    title: '利润率(%)',
    dataIndex: 'profitRate',
    key: 'profitRate',
    width: 100,
    align: 'center',
  },
  {
    title: '状态',
    dataIndex: 'state',
    key: 'state',
    width: 100,
    align: 'center',
  },
];

onMounted(async () => {
  currentRouter.value = await router;
  const currentId = currentRouter.value.currentRoute.query?.id;
  const currentMode = currentRouter.value.currentRoute.query?.mode;

  id.value = toNumber(currentId);

  // 根据传入的mode参数和id设置页面模式
  if (id.value) {
    if (currentMode === 'edit') {
      mode.value = 'edit'; // 编辑模式
    } else {
      mode.value = 'view'; // 查看模式
    }
    await get(id.value);
  } else {
    mode.value = 'add'; // 新增模式
    pascalCase.value = {};
  }
});

watch(
  () => currentRouter.value?.currentRoute.query,
  (newValue, oldValue) => {
    id.value = toNumber(newValue.id);
    const currentMode = newValue.mode;

    if (id.value) {
      if (currentMode === 'edit') {
        mode.value = 'edit'; // 编辑模式
      } else {
        mode.value = 'view'; // 查看模式
      }
      get(id.value);
    } else {
      mode.value = 'add'; // 新增模式
      pascalCase.value = {};
    }
  },
);

// 转换为简化结构的函数（新增时使用）
const convertToSimplifiedStructure = (giftData: any) => {
  const simplifiedData = {
    presentName: giftData.presentName || '',
    presentDesc: giftData.presentDesc || '',
    spuId: giftData.spuId || '',
    config: [],
    priceConfig: [],
  };

  // 转换 config 规格配置 - 新增时不包含id，listValueName为简单字符串数组
  if (giftData.config && giftData.config.length > 0) {
    simplifiedData.config = giftData.config.map((spec: any) => ({
      keyName: spec.keyName || '',
      listValueName: (spec.listValueName || []).map((value: any) =>
        value.valueName || value.name || value || ''
      ).filter((v: string) => v.trim()), // 过滤空值并确保是字符串数组
    }));
  }

  // 转换 priceConfig 价格配置 - 新增时不包含id，path只包含type和path
  if (giftData.priceConfig && giftData.priceConfig.length > 0) {
    simplifiedData.priceConfig = giftData.priceConfig.map((price: any) => ({
      valueName: Array.isArray(price.valueName)
        ? price.valueName
        : typeof price.valueName === 'string'
          ? price.valueName.split('/')
          : [],
      skuId: price.skuId || '',
      marketPrice: price.marketPrice || 0,
      costPrice: price.costPrice || 0,
      salePrice: price.salePrice || 0,
      profit: price.profit || 0,
      state: price.state !== undefined ? price.state : true,
      path: (price.path || []).map((img: any) => ({
        type: img.type || 0,
        path: img.path || '',
      })),
    }));
  }

  return simplifiedData;
};

// 转换编辑时提交数据为简化结构的函数
const convertToSimplifiedEditStructure = (editData: any) => {
  const simplifiedData = {
    id: editData.id || 0,
    presentName: editData.presentName || '',
    presentDesc: editData.presentDesc || '',
    config: [],
    priceConfig: [],
  };

  // 转换 config 规格配置（从编辑数据格式转为简化格式）
  if (editData.config && editData.config.length > 0) {
    simplifiedData.config = editData.config.map((spec: any, index: number) => ({
      id: spec.id || 0, // 🎯 使用原始ID，新增时为0
      listValueName: (spec.listValueName || []).map((valueName: string) => ({
        mppSpecKeyId: spec.id || 0, // 🎯 使用规格配置的ID作为mppSpecKeyId
        valueName: valueName || '',
      })),
    }));
  }

  // 转换 priceConfig 价格配置
  if (editData.priceConfig && editData.priceConfig.length > 0) {
    simplifiedData.priceConfig = editData.priceConfig.map((price: any) => {
      const priceItem: any = {
        valueName: Array.isArray(price.valueName) ? price.valueName : [],
        skuId: price.skuId || '',
        marketPrice: price.marketPrice || 0,
        costPrice: price.costPrice || 0,
        salePrice: price.salePrice || 0,
        profit: price.profit || 0,
        state: price.state !== undefined ? price.state : true,
        path: (price.path || []).map((img: any) => {
          if (img.id !== undefined && img.mainId !== undefined && img.tableName !== undefined) {
            // 老图片：包含完整元数据
            return {
              id: img.id || 0,
              mainId: img.mainId || 0,
              tableName: img.tableName || '',
              type: img.type || 0,
              path: img.path || '',
            };
          } else {
            // 新图片：只包含type和path
            return {
              type: img.type || 0,
              path: img.path || '',
            };
          }
        }),
      };

      // 🎯 只有老数据才设置id字段，新增数据不设置id
      if (price.id !== undefined && price.id !== null) {
        priceItem.id = price.id;
      }
      // 新增数据不设置id字段

      return priceItem;
    });
  }

  return simplifiedData;
};
//储存原始数据
const priceConfigDetails = ref()

const get = async (id: number) => {
  const data = await pascalCaseApi.get(id);
  if (data) {
    pascalCase.value = data;
    // 保存sourceType字段到单独的变量
    sourceType.value = (data as any).sourceType;
    // 如果是查看模式，需要设置规格配置数据
    if (data.config && Array.isArray(data.config)) {
      specTypes.value = data.config.map((item: any) => ({
        id: Date.now() + Math.random(),
        type: item.name || '',
        values: Array.isArray(item.listValueName)
          ? item.listValueName.map((valueItem: any) => valueItem.name || '')
          : [''], // 修复：提取对象数组中的 name 字段
        isOriginal: true, // 标记为原始数据
        originalValueCount: Array.isArray(item.listValueName) ? item.listValueName.length : 0, // 记录原始规格值数量
        originalId: item.id || 0, // 🎯 保存原始的规格配置ID
      }));
    }
    // 设置价格配置数据
    if (data.priceConfig && Array.isArray(data.priceConfig)) {
      priceConfigDetails.value = data.priceConfig
      console.log(priceConfigDetails.value, "priceConfigDetails.value");

      priceTableData.value = data.priceConfig.map((item: any, index: number) => ({
        key: index,
        specName: Array.isArray(item.valueName) ? item.valueName.join('/') : (item.valueName || ''), // 处理数组和字符串格式
        specCombination: Array.isArray(item.valueName) ? item.valueName : (item.valueName ? item.valueName.split('/') : []), // 统一转换为数组
        imageUrls: item.path && item.path.length > 0 ? item.path.map((p: { path: string }) => p.path) : [],
        imageMetadata:
          item.path && item.path.length > 0
            ? item.path.map((p: any) => ({
              id: p.id || 0,
              mainId: p.mainId || 0,
              tableName: p.tableName || '',
              type: p.type || 0,
            }))
            : [], // 🎯 保存原始图片的元数据
        marketPrice: item.marketPrice || null,
        costPrice: item.costPrice || null,
        salePrice: item.salePrice || null,
        profitRate: item.profit || null,
        skuId: item.skuId || '',
        state: item.state !== undefined ? item.state : true,
        isOriginal: true, // 标记为原始数据
        originalSkuId: item.skuId || '', // 保存原始SKU ID
        originalPriceConfigId: item.id || 0, // 🎯 保存原始的价格配置ID
      }));
    }
    console.log(priceTableData.value, 'priceTableData.value');
  }
};

const rules: FormProps['rules'] = {
  presentName: [{ required: true, message: '请输入礼品名称', trigger: 'blur' }],
  presentDesc: [{ required: true, message: '请输入礼品描述', trigger: 'blur' }],
  spuId: [{ required: true, message: '请输入SPU ID', trigger: 'blur' }],
};

const pascalCase = ref<IPascalCase>({});

//#region 上传相关，没有请删除

const baseUrl = import.meta.env.VITE_BUSINESS_URL;

const fileList = ref<Array<any>>([]);
const loading = ref<boolean>(false);
const imageUrl = ref<string>('');

const beforeUpload = (file: File, fileList: File[]): Promise<boolean> => {
  return new Promise((resolve) => {
    // 验证文件类型
    const validTypes = ['image/jpeg', 'image/png', 'image/jpg'];
    if (!validTypes.includes(file.type)) {
      if (fileList.indexOf(file) === fileList.length - 1 &&
        fileList.every(f => !validTypes.includes(f.type))) {
        message.error('所有文件格式错误，只能上传PNG/JPG/JPEG格式');
      }
      return resolve(false);
    }

    // 验证文件大小
    const maxSize = 2 * 1024 * 1024; // 2MB
    if (file.size > maxSize) {
      if (fileList.indexOf(file) === fileList.length - 1 &&
        fileList.every(f => f.size > maxSize)) {
        message.error('所有文件大小超过2MB限制');
      }
      return resolve(false);
    }

    // 验证图片尺寸
    const img = new Image();
    img.onload = () => {
      const { width, height } = img;
      // if (width !== 400 || height !== 400) {
      //   if (fileList.indexOf(file) === fileList.length - 1 && 
      //       fileList.every(f => /* 尺寸验证逻辑 */)) {
      //     message.error('所有图片尺寸不符合要求(400×400)');
      //   }
      //   resolve(false);
      // } else {
      //   resolve(true);
      // }
      resolve(true);
    };
    img.onerror = () => {
      if (fileList.indexOf(file) === fileList.length - 1 &&
        fileList.every(f => {
          try {
            const testImg = new Image();
            testImg.src = URL.createObjectURL(f);
            return false;
          } catch {
            return true;
          }
        })) {
        message.error('所有图片格式错误');
      }
      resolve(false);
    };
    img.src = URL.createObjectURL(file);
  });
};


const upload = async (options: any) => {
  loading.value = true;
  const formData = new FormData();
  formData.append('file', options.file);
  const res = await fileApi.upload(formData);
  const file = {
    ...options.file,
    name: options.file.name,
    url: baseUrl + res.path,
  };
  loading.value = false;
  fileList.value = [...fileList.value, file];
  imageUrl.value = baseUrl + res.path;
  pascalCase.value.imgUrl = baseUrl + res.path;

  options.onProgress(100);
  options.onSuccess(res, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
};

// 图片上传弹框相关状态
const imageUploadVisible = ref(false);
const currentUploadRecord = ref<IPriceTableItem | null>(null);
const uploadLoading = ref(false);
const tempImageUrls = ref<string[]>([]); // 临时图片数组

// 打开图片上传弹框
const openImageUploadModal = (record: IPriceTableItem) => {
  currentUploadRecord.value = record;
  // 复制当前记录的图片到临时数组
  tempImageUrls.value = record.imageUrls ? [...record.imageUrls] : [];
  // 🎯 确保imageMetadata存在，新增记录可能没有这个字段
  if (!(record as any).imageMetadata) {
    (record as any).imageMetadata = [];
  }
  imageUploadVisible.value = true;
};

// 关闭图片上传弹框
const closeImageUploadModal = () => {
  // 先应用图片更改
  if (currentUploadRecord.value) {
    currentUploadRecord.value.imageUrls = [...tempImageUrls.value];
    // 🎯 清理被删除的元数据，保持数组长度与imageUrls一致
    if ((currentUploadRecord.value as any).imageMetadata) {
      const metadata = (currentUploadRecord.value as any).imageMetadata;
      // 过滤掉被标记为删除的元数据，保持与当前图片数组的对应关系
      const filteredMetadata: any[] = [];
      let metaIndex = 0;

      for (let i = 0; i < tempImageUrls.value.length; i++) {
        // 找到下一个未被删除的元数据或null（新图片）
        while (metaIndex < metadata.length && metadata[metaIndex] && metadata[metaIndex].deleted) {
          metaIndex++;
        }

        if (metaIndex < metadata.length) {
          filteredMetadata.push(metadata[metaIndex]);
          metaIndex++;
        } else {
          filteredMetadata.push(null); // 新图片对应null
        }
      }

      (currentUploadRecord.value as any).imageMetadata = filteredMetadata;
    }
    message.success('图片更新成功');
  }

  // 然后关闭弹框并清理状态
  imageUploadVisible.value = false;
  currentUploadRecord.value = null;
  uploadLoading.value = false;
  tempImageUrls.value = [];
};

let pendingUploads = 0; // 跟踪进行中的上传数量
let hasShownMessage = false; // 标记是否已显示消息

const uploadImageInModal = async (options: any) => {
  uploadLoading.value = true;
  pendingUploads++; // 增加计数器

  try {
    const formData = new FormData();
    formData.append('file', options.file);
    const res = await fileApi.upload(formData);
    tempImageUrls.value.push(baseUrl + res.path);

    // 🎯 处理图片元数据：优先复用已删除图片的元数据结构
    if (currentUploadRecord.value && (currentUploadRecord.value as any).imageMetadata) {
      const metadata = (currentUploadRecord.value as any).imageMetadata;
      // 查找第一个被标记为删除的元数据位置
      const deletedIndex = metadata.findIndex((meta: any) => meta && meta.deleted);

      if (deletedIndex !== -1) {
        // 复用已删除图片的元数据结构，只更新path
        const originalMeta = metadata[deletedIndex];
        metadata[deletedIndex] = {
          id: originalMeta.id || 0,
          mainId: originalMeta.mainId || 0,
          tableName: originalMeta.tableName || '',
          type: originalMeta.type || 0,
          path: baseUrl + res.path,
          deleted: false // 移除删除标记
        };
      } else {
        // 没有可复用的元数据，添加新的（新图片格式）
        metadata.push(null); // 新图片对应的元数据为null
      }
    }

    options.onProgress(100);
    options.onSuccess(res, options.file);
  } catch (error) {
    options.onError(error);
    throw error;
  } finally {
    pendingUploads--; // 减少计数器
    uploadLoading.value = pendingUploads > 0;

    // 当所有上传完成且未显示过消息时
    if (pendingUploads === 0 && !hasShownMessage) {
      hasShownMessage = true;
      message.success('所有照片上传完成');
    }
  }
};



// // 弹框中的图片上传
// const uploadImageInModal = async (options: any) => {
//   uploadLoading.value = true;
//   console.log(options, 'options');

//   try {
//     const formData = new FormData();
//     formData.append('file', options.file);
//     const res = await fileApi.upload(formData);

//     console.log(res, 'res');

//     // 添加到临时数组
//     tempImageUrls.value.push(baseUrl + res.path);



//     // message.success('上传成功');

//     options.onProgress(100);
//     options.onSuccess(res, options.file);
//   } catch (error) {
//     // message.error('上传失败');
//     options.onError(error);
//   } finally {
//     uploadLoading.value = false;
//   }
// };

// 删除图片
const removeImage = (imageIndex: number) => {
  tempImageUrls.value.splice(imageIndex, 1);
  // 🎯 标记删除位置但保留元数据结构，用于后续复用
  if (currentUploadRecord.value && (currentUploadRecord.value as any).imageMetadata) {
    // 将删除位置的元数据标记为已删除，而不是直接删除
    (currentUploadRecord.value as any).imageMetadata[imageIndex] = { ...((currentUploadRecord.value as any).imageMetadata[imageIndex] || {}), deleted: true };
  }
  message.success('删除成功');
};

//#endregion

// 添加规格值
const addSpecValue = (specIndex: number) => {
  specTypes.value[specIndex].values.push('');
};

// 删除规格值
const removeSpecValue = (specIndex: number, valueIndex: number) => {
  const spec = specTypes.value[specIndex] as any;

  // 编辑模式下，不能删除原始规格值，只能删除新增的
  if (isEditMode.value && spec.isOriginal && valueIndex < spec.originalValueCount) {
    message.warning('编辑模式下不能删除原有的规格值');
    return;
  }

  if (spec.values.length > 1) {
    spec.values.splice(valueIndex, 1);
  }
};

// 新增规格类型
const addSpecType = () => {
  // 编辑模式下不能新增规格类型
  if (isEditMode.value) {
    message.warning('编辑模式下不能新增规格类型');
    return;
  }

  specTypes.value.push({
    id: Date.now(),
    type: '',
    values: [''],
  });
};

// 删除规格类型
const removeSpecType = (specIndex: number) => {
  // 编辑模式下不能删除规格类型
  if (isEditMode.value) {
    message.warning('编辑模式下不能删除规格类型');
    return;
  }

  console.log('删除规格类型', specIndex, specTypes.value.length);
  if (specTypes.value.length > 1) {
    specTypes.value.splice(specIndex, 1);
    message.success('删除成功');
  } else {
    message.warning('至少需要保留一个规格类型');
  }
};

// 计算利润率
const calculateProfitRate = (costPrice: number, salePrice: number): number => {
  if (costPrice <= 0) return 0;
  return Number((((salePrice - costPrice) / costPrice) * 100).toFixed(2));
};

// 当成本价或销售价变化时更新利润率
const updateProfitRate = (record: IPriceTableItem) => {
  record.profitRate = calculateProfitRate(record.costPrice || 0, record.salePrice || 0);
};

// 生成规格组合的笛卡尔积
const generateSpecCombinations = (): IPriceTableItem[] => {
  // 过滤掉空的规格类型和规格值
  const validSpecs = specTypes.value
    .filter((spec) => spec.type.trim() && spec.values.some((value) => value.trim()))
    .map((spec) => ({
      type: spec.type.trim(),
      values: spec.values.filter((value) => value.trim()),
    }));

  if (validSpecs.length === 0) {
    return [];
  }

  // 生成笛卡尔积
  const combinations: string[][] = [];

  const generateCombination = (index: number, currentCombination: string[]) => {
    if (index === validSpecs.length) {
      combinations.push([...currentCombination]);
      return;
    }

    for (const value of validSpecs[index].values) {
      currentCombination.push(value);
      generateCombination(index + 1, currentCombination);
      currentCombination.pop();
    }
  };

  generateCombination(0, []);

  // 转换为表格数据格式
  return combinations.map((combination, index) => ({
    key: index,
    specName: combination.join('/'),
    specCombination: combination, // 保存原始数组
    imageUrls: [],
    marketPrice: null,
    costPrice: null,
    salePrice: null,
    profitRate: null,
    skuId: '',
    state: true,
  }));
};

// 滚动到顶部的通用方法
const scrollToTop = () => {
  nextTick(() => {
    // 尝试多种滚动方式，确保在不同布局下都能工作

    // 方法1: 滚动window
    window.scrollTo({ top: 0, behavior: 'smooth' });

    // 方法2: 滚动document.documentElement
    document.documentElement.scrollTo({ top: 0, behavior: 'smooth' });

    // 方法3: 滚动document.body
    document.body.scrollTo({ top: 0, behavior: 'smooth' });

    // 方法4: 查找可能的滚动容器并滚动
    const scrollContainers = [
      document.querySelector('.ant-layout-content'),
      document.querySelector('.content'),
      document.querySelector('.main-content'),
      document.querySelector('.router-view'),
      document.querySelector('[class*="content"]'),
      document.querySelector('[class*="container"]')
    ];

    scrollContainers.forEach(container => {
      if (container && container.scrollTo) {
        container.scrollTo({ top: 0, behavior: 'smooth' });
      }
    });

    // 方法5: 强制立即滚动（备用方案）
    setTimeout(() => {
      window.scrollTo(0, 0);
      document.documentElement.scrollTop = 0;
      document.body.scrollTop = 0;
    }, 100);
  });
};

// 步骤导航
const nextStep = () => {
  if (isViewMode.value) {
    // 查看模式直接跳转到下一步
    currentStep.value = 1;
    scrollToTop();
    return;
  }

  // 新增或编辑模式需要验证表单
  // 先检查基础字段是否为空
  if (!pascalCase.value.presentName || !pascalCase.value.presentDesc || !pascalCase.value.spuId) {
    message.error('请完善基础配置信息');
    return;
  }

  // 验证表单
  from.value
    .validateFields(['presentName', 'presentDesc', 'spuId'])
    .then(() => {
      if (mode.value === 'add') {
        // 新增模式：重新生成规格组合
        priceTableData.value = generateSpecCombinations();
      } else if (mode.value === 'edit') {
        // 编辑模式：生成新的规格组合并与现有数据合并
        const newCombinations = generateSpecCombinations();
        const newCombinationNames = newCombinations.map((item) => item.specName);

        // 过滤掉不再存在的规格组合（保留原始数据中仍然有效的组合）
        const filteredExistingData = priceTableData.value.filter((item) => {
          // 保留原始数据中仍然存在的组合
          if ((item as any).isOriginal) {
            return newCombinationNames.includes(item.specName);
          }
          // 保留新增的数据中仍然存在的组合
          return newCombinationNames.includes(item.specName);
        });

        // 找出需要新增的组合
        const existingCombinations = filteredExistingData.map((item) => item.specName);
        const combinationsToAdd = newCombinations.filter((newItem) => !existingCombinations.includes(newItem.specName));

        // 为新组合添加标记
        combinationsToAdd.forEach((item) => {
          (item as any).isOriginal = false;
        });

        priceTableData.value = [...filteredExistingData, ...combinationsToAdd];
      }
      currentStep.value = 1;
      console.log('生成的规格组合数量:', priceTableData.value.length);
      console.log(priceTableData.value,"priceTableData.value");
      

      scrollToTop();
    })
    .catch((error: any) => {
      // 表单验证失败
      console.error('表单验证失败:', error);
      message.error('请完善基础配置信息');
    });
};

const prevStep = () => {
  currentStep.value = 0;
  scrollToTop();
};

// 取消操作
const handleCancel = () => {
  if (isCloseLastTab) {
    // 关闭当前页签
    isCloseLastTab.value = true;
  }
  currentRouter.value.push({ path: '/mice-merchant/pascalCase/index' });
};

// 切换到编辑模式
const switchToEditMode = () => {
  mode.value = 'edit';
};

const handleOk = () => {
  if (isViewMode.value) {
    // 查看模式直接返回
    handleCancel();
    return;
  }

  confirmLoading.value = true;
  from.value
    .validate()
    .then(() => {
      console.log(priceTableData.value, "priceTableData.value");

      // 检查价格配置中状态为上传的 skuId 是否必填
      const hasEmptySkuId = priceTableData.value.some((item) => (!item.skuId || item.skuId.toString().trim() === '') && item.state == true);
      const hasEmptyMarketPrice = priceTableData.value.some((item) => (!item.marketPrice || item.marketPrice === 0) && item.state == true);
      const hasEmptyCostPrice = priceTableData.value.some((item) => (!item.costPrice || item.costPrice === 0) && item.state == true);
      const hasEmptySalePrice = priceTableData.value.some((item) => (!item.salePrice || item.salePrice === 0) && item.state == true);
      if (hasEmptySkuId) {
        message.error('上架产品的 SKU ID 不能为空');
        confirmLoading.value = false;
        return;
      }
      if (hasEmptyMarketPrice) {
        message.error('上架产品的市场价不能为空');
        confirmLoading.value = false;
        return;
      }
      if (hasEmptyCostPrice) {
        message.error('上架产品的成本价不能为空');
        confirmLoading.value = false;
        return;
      }
      if (hasEmptySalePrice) {
        message.error('上架产品的销售价不能为空');
        confirmLoading.value = false;
        return;
      }
      // 检查价格配置中的 skuId 是否必填
      const hasEmptyImage = priceTableData.value.some((item) => (!item.imageUrls || item.imageUrls.length == 0) && item.state == true);
      if (hasEmptyImage) {
        message.error(`上架产品的图片不能为空`);
        confirmLoading.value = false;
        return;
      }

      // 检查价格配置中的 skuId 是否重复
      const skuIds = priceTableData.value.map((item) => item.skuId).filter((id) => id);
      if (new Set(skuIds).size !== skuIds.length) {
        message.error('价格配置中的 SKU ID 不能重复');
        confirmLoading.value = false;
        return;
      }
      console.log(specTypes.value, "specTypes.value");

      // 转换规格配置数据
      const config = specTypes.value
        .map((spec) => {
          if (isEditMode.value && (spec as any).isOriginal) {
            // 编辑模式下，对于原始规格类型，只提交新增的规格值
            const originalValueCount = (spec as any).originalValueCount || 0;
            const newValues = spec.values.slice(originalValueCount).filter((value) => value.trim());

            if (newValues.length > 0) {
              return {
                id: (spec as any).originalId || 0, // 🎯 编辑时需要原始规格类型的ID
                keyName: spec.type.trim(),
                listValueName: newValues.map((value) => ({
                  mppSpecKeyId: (spec as any).originalId || 0,
                  valueName: value
                })), // 转换为对象数组格式
              };
            }
            return null; // 没有新增规格值时返回null
          } else if (spec.type.trim()) {
            // 新增模式或新增的规格类型，提交所有数据
            return {
              keyName: spec.type.trim(),
              listValueName: spec.values.filter((value) => value.trim()), // 新增时使用简单字符串数组
            };
          }
          return null;
        })
        .filter((spec) => spec !== null) as any[]; // 过滤掉null值并断言类型
      console.log('6666');

      // 转换价格配置数据
      const priceConfig = priceTableData.value.map((item, index) => {
        const configItem: any = {
          valueName: Array.isArray(item.specCombination)
            ? item.specCombination
            : item.specName
              ? item.specName.split('/')
              : [], // 新增时使用数组格式
          skuId: Number(item.skuId) || null, // 转换为数字格式以匹配类型定义
          marketPrice: item.marketPrice || 0,
          costPrice: item.costPrice || 0,
          salePrice: item.salePrice || 0,
          profit: item.profitRate || 0,
          state: item.state,
          path:
            item.imageUrls && item.imageUrls.length > 0
              ? item.imageUrls.map((url, imgIndex) => {
                // 🎯 检查是否为原始图片（通过imageMetadata数组）
                const originalImageMeta = (item as any).imageMetadata && (item as any).imageMetadata[imgIndex];

                if (originalImageMeta && !originalImageMeta.deleted) {
                  // 老图片或复用的图片：保留完整的元数据，使用当前的url
                  return {
                    id: originalImageMeta.id || null,
                    mainId: originalImageMeta.mainId || null,
                    tableName: originalImageMeta.tableName || '',
                    type: originalImageMeta.type || String(FileTypeConstant.PICTURE.code),
                    path: url, // 使用当前的图片路径
                  };
                } else {
                  // 新图片：只包含type和path
                  return {
                    type: String(FileTypeConstant.PICTURE.code),
                    path: url,
                  };
                }
              })
              : [],
        };

        // 🎯 区分老数据和新增数据的id处理
        if ((item as any).isOriginal && (item as any).originalSkuId) {
          configItem.id = (item as any).originalPriceConfigId || 0; // 老数据使用原始的价格配置ID
        } else {
          configItem.id = null; // 新增数据传null
        }

        // 🎯 编辑模式下添加 merchantProductPresentId 字段
        if (isEditMode.value && id.value) {
          configItem.merchantProductPresentId = id.value;
        }
        console.log('priceConfigDetails.value', priceConfigDetails.value);
        if (isEditMode.value) {
          const configId = priceConfigDetails.value.find((price) => price?.id == item.originalPriceConfigId)
          if (configId && item.skuId == configId.skuId) {
            delete configItem.skuId
          }
          console.log(configId, "configId");
        }
        return configItem;
      });
      console.log('999');

      const data = {
        // id: pascalCase.value.id,
        spuId: pascalCase.value.spuId,
        presentName: pascalCase.value.presentName || '',
        presentDesc: pascalCase.value.presentDesc || '',
        sourceType: sourceType.value, // 添加sourceType字段
        config: config,
        priceConfig: priceConfig,
      };
      console.log(data, "data");


      if (isEditMode.value) {
        // 编辑模式
        const simplifiedEditData = convertToSimplifiedEditStructure(data);
        console.log(JSON.stringify(simplifiedEditData, null, 2));

        // 🎯 调用编辑API
        pascalCaseApi
          .presentEdit({ ...data, id: id.value })
          .then((res) => {
            message.success('编辑成功!');
            if (isCloseLastTab) {
              // 关闭当前页签
              isCloseLastTab.value = true;
            }
            currentRouter.value.push({ path: '/mice-merchant/pascalCase/index' });
          })
          .catch((error) => {
            console.error('编辑失败:', error);
            message.error('编辑失败，请重试');
          })
          .finally(() => {
            confirmLoading.value = false;
          });
      } else {
        // 新增模式 - 转换为简化结构
        const simplifiedData = convertToSimplifiedStructure(data);
        console.log('新增数据结构:', JSON.stringify(simplifiedData, null, 2));

        pascalCaseApi
          .save(simplifiedData)
          .then((res) => {
            message.success(`新增成功!`);
            if (isCloseLastTab) {
              // 关闭当前页签
              isCloseLastTab.value = true;
            }
            currentRouter.value.push({ path: '/mice-merchant/pascalCase/index' });
          })
          .finally(() => {
            confirmLoading.value = false;
          });
      }
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};

const vSelectOnFocus = {
  mounted(el: HTMLElement) {
    const input = el.querySelector('input')
    const handleFocus = (e: Event) => {
      setTimeout(() => (e.target as HTMLInputElement).select(), 10)
    }
    input?.addEventListener('focus', handleFocus)
    // 存储回调函数以便卸载时移除
    el._selectOnFocusHandler = handleFocus
  },
  unmounted(el: HTMLElement & { _selectOnFocusHandler?: EventListener }) {
    const input = el.querySelector('input')
    if (el._selectOnFocusHandler) {
      input?.removeEventListener('focus', el._selectOnFocusHandler)
    }
  }
}

</script>

<template>
  <div class="gift-edit-container">
    <div class="gift-edit-content">
      <h-form ref="from" :model="pascalCase" :label-col="{ span: 6 }" :rules="rules" hide-required-mark>
        <!-- 第一步：基础配置 + 规格配置 -->
        <div v-show="currentStep === 0">
          <!-- 基础配置 -->
          <h-divider orientation="left">基础配置</h-divider>

          <h-form-item label="礼品名称" name="presentName">
            <h-input v-model:value="pascalCase.presentName" placeholder="请输入" class="gift-input"
              :disabled="isViewMode" />
          </h-form-item>

          <h-form-item label="礼品描述" name="presentDesc">
            <h-input v-model:value="pascalCase.presentDesc" placeholder="请输入" class="gift-input"
              :disabled="isViewMode" />
          </h-form-item>

          <h-form-item label="SPU" name="spuId">
            <h-input v-model:value="pascalCase.spuId" placeholder="请输入SPU" class="gift-input"
              :disabled="isViewMode || isEditMode" />
          </h-form-item>

          <!-- 规格配置 -->
          <h-divider orientation="left">规格配置</h-divider>

          <div v-for="(spec, specIndex) in specTypes" :key="spec.id" class="spec-group">
            <DeleteOutlined v-if="!isViewMode && !isEditMode && specTypes.length > 1" class="delete-icon-all"
              @click.stop="removeSpecType(specIndex)" title="删除此规格类型" />
            <h-form-item label="规格类型">
              <h-input v-model:value="spec.type" placeholder="请输入" class="gift-input"
                :disabled="isViewMode || isEditMode" />
            </h-form-item>

            <h-form-item label="规格描述">
              <div class="spec-values-container">
                <div v-for="(value, valueIndex) in spec.values" :key="valueIndex" class="spec-value-item">
                  <h-input v-model:value="spec.values[valueIndex]" placeholder="请输入" class="gift-input"
                    :disabled="isViewMode || (isEditMode && (spec as any).isOriginal && valueIndex < (spec as any).originalValueCount)"
                    style="margin-right: 24px;" />
                  <div
                    v-if="spec.values.length > 1 && !isViewMode && !(isEditMode && (spec as any).isOriginal && valueIndex < (spec as any).originalValueCount)"
                    @click="removeSpecValue(specIndex, valueIndex)" class="addImg">
                    <img src="@/assets/image/orderList/delete.png" alt="">
                  </div>
                  <div v-if="valueIndex === spec.values.length - 1 && !isViewMode" @click="addSpecValue(specIndex)"
                    class="addImg">
                    <img src="@/assets/image/orderList/add.png" alt="">
                  </div>

                </div>
              </div>
            </h-form-item>
          </div>
          <div v-if="!isViewMode && !isEditMode" class="add-spec-item">
            <h-button @click="addSpecType" class="add-spec-button" ghost>
              <PlusOutlined /> 新增规格
            </h-button>
          </div>
        </div>

        <!-- 第二步：价格配置 -->
        <div v-show="currentStep === 1">
          <h-divider orientation="left">价格配置</h-divider>

          <h-table :columns="priceColumns" :data-source="priceTableData" :pagination="{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
          }" size="small" bordered width="100%">
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'skuId'">
                <h-input-number v-model:value="record.skuId" :min="0" :precision="0" class="table-input"
                  :disabled="isViewMode || (isEditMode && (record as any).isOriginal)" />
              </template>
              <template v-if="column.key === 'imageUrl'">
                <div class="image-upload-container">
                  <h-button v-if="!isViewMode" size="small" type="link" @click="openImageUploadModal(record as any)">
                    {{ record.imageUrls && record.imageUrls.length > 0 ? '管理图片' : '上传图片' }}
                  </h-button>
                  <div class="image-preview-container" v-if="record.imageUrls && record.imageUrls.length > 0">
                    <a-image-preview-group>
                      <a-image :src="item" class="preview-image"
                        v-for="(item, index) in [...record.imageUrls].reverse()" :key="index"
                        :style="{ display: index === 0 ? 'inline-block' : 'none' }" />
                    </a-image-preview-group>
                    <p class="font">{{ record.imageUrls.length }}张</p>
                  </div>
                  <span v-if="(!record.imageUrls || record.imageUrls.length === 0) && isViewMode"
                    class="no-image-text">暂无图片</span>
                </div>
              </template>
              <template v-if="column.key === 'marketPrice'">
                <h-input-number v-model:value="record.marketPrice" :min="0" :precision="2" class="table-input"
                  :disabled="isViewMode || record.state == false" v-select-on-focus />
              </template>
              <template v-if="column.key === 'costPrice'">
                <h-input-number v-model:value="record.costPrice" :min="0" :precision="2" class="table-input"
                  :disabled="isViewMode || record.state == false" @change="updateProfitRate(record as IPriceTableItem)"
                  v-select-on-focus />
              </template>
              <template v-if="column.key === 'salePrice'">
                <h-input-number v-model:value="record.salePrice" :min="0" :precision="2" class="table-input"
                  :disabled="isViewMode || record.state == false" @change="updateProfitRate(record as IPriceTableItem)"
                  v-select-on-focus />
              </template>
              <template v-if="column.key === 'profitRate'">
                <h-input-number v-model:value="record.profitRate" :min="0" :max="100" :precision="2" class="table-input"
                  :disabled="true" :formatter="(value) => value == 0 ? 0+'%' : `${value}%`"
                  :parser="(value) => value.replace('%', '')" v-select-on-focus />
              </template>
              <template v-if="column.key === 'state'">
                <h-select v-model:value="record.state" class="table-input" :disabled="isViewMode">
                  <h-select-option :value="true">上架</h-select-option>
                  <h-select-option :value="false">下架</h-select-option>
                </h-select>
              </template>
            </template>
          </h-table>
        </div>
      </h-form>
    </div>
    <!-- 操作按钮 -->
    <div class="submit-btn">
      <h-button @click="handleCancel" class="cancel-btn">
        {{ isViewMode ? '返回' : '取消' }}
      </h-button>

      <!-- 第一步按钮 -->
      <template v-if="currentStep === 0">
        <h-button type="primary" @click="nextStep" class="primary-btn">下一步</h-button>
        <h-button v-if="isViewMode" type="primary" @click="switchToEditMode" style="margin-left: 10px"
          class="primary-btn">编辑</h-button>
      </template>

      <!-- 第二步按钮 -->
      <template v-if="currentStep === 1 && !isViewMode">
        <h-button @click="prevStep" class="prev-btn">上一步</h-button>
        <h-button type="primary" @click="handleOk" :loading="confirmLoading" class="primary-btn">确定</h-button>
      </template>

      <!-- 查看模式下的第二步按钮 -->
      <template v-if="currentStep === 1 && isViewMode">
        <h-button @click="prevStep" class="prev-btn">上一步</h-button>
        <h-button type="primary" @click="switchToEditMode" class="primary-btn">编辑</h-button>
      </template>
    </div>
    <!-- 图片上传弹框 -->
    <h-modal v-model:open="imageUploadVisible" title="图片管理" width="800px" @cancel="imageUploadVisible = false">
      <div class="image-upload-modal-content">
        <!-- 图片展示区域 -->
        <div class="image-gallery">
          <h-row :gutter="[16, 16]">
            <!-- 已上传的图片 -->
            <h-col :span="8" v-for="(imageUrl, index) in tempImageUrls" :key="index">
              <div class="image-card">
                <div class="image-container">
                  <h-image :src="imageUrl" :preview="false" class="uploaded-image"
                    fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklQVR4Ae3dP3Ik1RnG4W+FgYxN" />
                </div>
                <div class="image-content">
                  <div class="image-actions" v-if="!isViewMode">
                    <h-button type="text" danger size="small" @click="removeImage(index)" class="delete-btn">
                      <DeleteOutlined /> 删除
                    </h-button>
                  </div>
                </div>
              </div>
            </h-col>

            <!-- 上传按钮卡片 -->
            <h-col :span="8" v-if="!isViewMode">
              <div class="upload-card">
                <h-upload :show-upload-list="false" :before-upload="beforeUpload" :custom-request="uploadImageInModal"
                  accept="image/*" :disabled="uploadLoading" class="upload-area" multiple>
                  <div class="upload-content">
                    <div class="upload-icon">
                      <UploadOutlined v-if="!uploadLoading" />
                      <LoadingOutlined v-else />
                    </div>
                    <div class="upload-text">
                      {{ uploadLoading ? '上传中...' : '上传图片' }}
                    </div>
                    <div class="upload-tips" v-if="!uploadLoading">
                      <div>建议尺寸：400×400</div>
                      <div>大小不超过2MB</div>
                      <div>格式：png/jpg/jpeg</div>
                    </div>
                  </div>
                </h-upload>
              </div>
            </h-col>
          </h-row>

          <!-- 空状态 -->
          <div v-if="tempImageUrls.length === 0 && isViewMode" class="empty-state">
            <h-empty description="暂无图片" />
          </div>
        </div>
      </div>

      <template #footer>
        <h-button @click="closeImageUploadModal">完成</h-button>
      </template>
    </h-modal>

    <!-- 添加独立的预览弹窗 -->
    <div class="global-preview-overlay" v-if="previewVisible" @click="previewVisible = false">
      <div class="global-preview-container" @click.stop>
        <div class="global-preview-header">
          <span>{{ previewTitle }}</span>
          <button class="global-preview-close" @click="previewVisible = false">×</button>
        </div>
        <div class="global-preview-main">
          <img :src="previewImage" alt="预览图片" />
        </div>
        <div class="global-preview-nav" v-if="previewImages.length > 1">
          <div v-for="(img, i) in previewImages" :key="i" class="global-preview-thumbnail"
            :class="{ active: img === previewImage }" @click="modifyPicture(img)">
            <img :src="img" :alt="img" />
            <CloseOutlined class="close-icon" @click.stop="handleDelete(i)" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.gift-edit-container {
  background-color: #ffff;
  width: 100%;
  min-height: 100vh;
  padding: 20px;
  padding-bottom: 80px;
  position: relative;
}

.gift-edit-content {
  width: 100%;
  height: 100%;
}

.gift-input {
  width: 300px;
}

.spec-values-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.spec-value-item {
  display: flex;
  align-items: center;
}

.delete-icon {
  color: #ff4d4f;
  cursor: pointer;
  font-size: 14px;
}

.add-spec-item {
  margin-bottom: 24px;
  display: flex;
  justify-content: center;
  width: 100%;
}

.add-spec-button {
  width: 100%;
  max-width: 680px;
  height: 40px;
  border-style: dashed;
  color: #000000;
  border-color: #d9d9d9;
}

.table-input {
  width: 100%;
}

.image-upload-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.image-preview-container {
  position: relative;
}

:deep(.preview-image) {
  width: 40px;
  height: 40px !important;
  object-fit: cover;
  border-radius: 4px;
}

.close-icon {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff4d4f;
  color: white;
  border-radius: 50%;
  padding: 2px;
  cursor: pointer;
  font-size: 10px;
}

.no-image-text {
  color: #999;
  font-size: 12px;
}

.submit-btn {
  width: calc(100% - 245px);
  display: flex;
  justify-content: right;
  background-color: #fff;
  box-shadow: 1px 0px 10px #ccc;
  padding: 10px 20px;
  position: fixed;
  bottom: 0;
  right: 15px;
}

.cancel-btn {
  margin-right: 16px;
  width: 100px;
}

.primary-btn {
  width: 100px;
}

.prev-btn {
  margin-right: 16px;
  width: 100px;
}

:deep(.ant-divider-horizontal) {
  min-width: 80%;
  width: 80%;
}

:deep(.ant-divider-horizontal.ant-divider-with-text-left) {
  margin: 16px 0;

  .ant-divider-inner-text {
    font-weight: 600;
    color: #1890ff;
  }
}

:deep(.ant-table-tbody > tr > td) {
  padding: 8px;
}

.spec-group {
  position: relative;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid #e8e8e8;
  background: #fafafa;
}

.delete-icon-all {
  color: #ff4d4f;
  position: absolute;
  top: 16px;
  right: 16px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s;
  z-index: 10;
  padding: 4px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4px;

  &:hover {
    color: #ff7875;
    transform: scale(1.1);
    background: rgba(255, 255, 255, 1);
  }
}

/* 全局预览弹窗样式 */
.global-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.global-preview-container {
  background-color: white;
  border-radius: 4px;
  width: 90%;
  max-width: 800px;
  height: 500px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.global-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
}

.global-preview-close {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #999;
}

.global-preview-main {
  padding: 16px;
  text-align: center;
  overflow: auto;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    display: block;
    max-width: 100%;
    max-height: 60vh;
    object-fit: contain;
    margin: auto;
  }
}

.global-preview-nav {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 16px;
  justify-content: center;
  border-top: 1px solid #eee;
  background-color: #f9f9f9;
}

.global-preview-thumbnail {
  width: 60px;
  height: 60px;
  border: 2px solid transparent;
  cursor: pointer;
  // overflow: hidden;
  position: relative;

  &.active {
    border-color: #1890ff;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &:hover {
    opacity: 0.8;
  }
}

.font {
  font-size: 10px;
  color: #1890ff;
  margin-bottom: 0px;
}

/* 图片上传弹框样式 */
.image-upload-modal-content {
  max-height: 500px;
  overflow-y: auto;
  overflow-x: hidden;
  /* 隐藏横向滚动条 */
}

.upload-button-area {
  margin-bottom: 20px;
}

.image-gallery {
  margin-top: 20px;
  width: 100%;

  :deep(.ant-row) {
    margin-left: -8px !important;
    margin-right: -8px !important;
    width: calc(100% + 16px) !important;
  }

  :deep(.ant-col) {
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
}

.image-card {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #1890ff;
  }
}

.image-container {
  height: 140px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 8px 8px 0 0;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-content {
  padding: 12px;
  background: #fff;
}

.image-name {
  font-size: 14px;
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.image-actions {
  display: flex;
  justify-content: center;
  margin-top: 4px;
}

.delete-btn {
  color: #ff4d4f;

  &:hover {
    color: #ff7875;
    background: rgba(255, 77, 79, 0.1);
  }
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}

/* 上传卡片样式 */
.upload-card {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  height: 100%;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #1890ff;
  }
}

.upload-area {
  width: 100%;
  height: 100%;

  :deep(.ant-upload) {
    width: 100%;
    height: 100%;
    display: block;
  }
}

.upload-content {
  height: 188px;
  /* 与图片卡片高度保持一致 (140px图片容器 + 48px内容区域) */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border-radius: 8px;
  transition: all 0.3s;
  padding: 16px 12px;
  box-sizing: border-box;

  &:hover {
    background: #f0f8ff;
  }
}

.upload-icon {
  font-size: 28px;
  color: #1890ff;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  margin-bottom: 8px;
}

.upload-tips {
  font-size: 12px;
  color: #999;
  text-align: center;
  line-height: 1.4;

  div {
    margin-bottom: 2px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.addImg {
  cursor: pointer;

  img {
    width: 20px;
    height: 20px;
    margin-right: 16px;
  }
}
</style>
